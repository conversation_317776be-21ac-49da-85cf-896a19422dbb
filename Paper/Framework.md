
## 方法论与框架设计

本方法的核心原则在于，**将离散的、通过离线剖析（offline-profiling）获得的通信追踪数据，转化为连续且可微的数学表示**。我们利用傅里叶变换对周期性的流量模式进行建模，从而能够采用基于梯度的优化算法，来确定每个作业的最优时序调度。这一方法论避免了高成本且耗时的在线剖析开销，为解决关键的性能瓶颈提供了一个轻量级而强大的解决方案。

### 系统前提与假设

所提出的框架在以下明确定义的假设下运行：

- **并行策略**: 当前模型专为**数据并行**训练范式设计，该范式具有鲜明的低带宽计算阶段（前向/反向传播）和高带宽通信阶段（如AllReduce梯度同步）。
- **网络环境**: 我们假设集群网络环境稳定，具有**确定性的路由协议和固定的路由规则**。这确保了对于一个给定的作业放置方案，其通信模式是可复现和可预测的，从而简化了模型，无需处理瞬态网络拥塞或动态路由变化。
- **数据来源**: 框架依赖一个预先构建的、针对不同DNN模型和配置的**离线剖析数据库**。一次性剖析的成本被分摊到多次调度决策中，从而消除了在线性能开销。

### 阶段一：放置候选的生成 (Placement Candidate Generation)

初始阶段利用任务调度器在粗粒度资源分配上的优势。我们的系统与Themis或Pollux等调度器集成，请求为一组给定的作业生成 $N$ 个（例如，$N=10-20$）不同的放置候选。每个候选方案都明确了作业到GPU及服务器的映射关系，并考虑了集群的网络拓扑（如叶脊网络结构）。这些放置方案为后续的细粒度通信优化提供了基础场景。

### 阶段二：可微通信模型的推导 (Derivation of Differentiable Communication Models)

针对每个放置候选，我们推导出一个聚合网络流量的数学模型。这是将静态放置方案转化为动态的、连续时间维度的带宽需求表示的关键步骤。此过程细分为以下步骤：

1.  **共享路径分析 : 对放置方案的拓扑进行分析，识别所有共享的网络链路及其上承载的作业集合。这决定了不同作业的通信流量将如何以及在何处叠加。
2.  **任务识别与模式检索 **: 对于方案中的每个作业 $j$，我们首先识别其**任务类型**（例如，模型名称、批次大小等配置）。随后，根据任务类型，从离线剖析数据库中检索其对应的、离散的通信追踪数据。
3.  **连续时间模型构建**: 对检索到的离散数据应用**傅里叶变换**，将其分解为构成频率。通过**保留能量最高的K个低频分量**（其中K为超参数）并滤除其余高频噪声，我们为每个作业 $j$ 重构出一个精确、平滑且可微的流量模型函数 $g_j(t)$。
4.  **聚合负载叠加 : 将各个作业的流量模型 $\{g_j(t)\}$ 根据第一步的共享路径分析结果进行叠加，从而为每个存在竞争的链路生成一个总的、聚合的带宽利用率函数 $L(t)$。

此阶段的输出是一组可微函数 $\{L(t)\}$，精确地代表了在特定放置候选下，关键链路上的预期带宽争用情况。

### 阶段三：通信调度的优化 (Optimization of Communication Schedules)

在获得网络负载的可微模型后，我们将调度问题转化为一个连续域的优化任务。

- **优化目标**: 优化器的目标是求解一组可学习的调度参数，以最小化一个复合损失函数。该函数主要包含三部分：
    1.  **带宽争用损失**: 主要项为 $\int_{0}^{T_{period}} F(L(t)) dt$，用于惩罚在一个调度周期 $T_{period}$ 内的过高带宽利用率。$F(L)$ 是我们设计的严格凸度量函数，它在理想利用率 $(1-\mu)$ 处达到全局最小值：
        $$
        F(L) = \alpha (L-(1-\mu))^2 + \beta \left[ e^{\gamma(L-(1-\mu))} - 1 - \gamma (L-(1-\mu)) \right]
        $$
    2.  **延迟正则化项**: $\lambda_D \sum_{j} (\Delta T_j)$，用以惩罚过长的平均调度延迟，确保作业能够及时推进，其中 $\lambda_D$ 为惩罚系数。
    3.  **调度间隔正则化项**: 用于惩罚低效的静默窗口排列（例如，避免窗口过于集中或分散），以维持系统的高吞吐量。

- **优化机制**: **优化机制通过调整一组可学习的调度参数来实现**。具体而言，每个作业 $j$ 的原始流量模型 $g_j(t)$ 被变换为 $g'_j(t) = g_j(t - \Delta T_j) \cdot S_j(t)$。其中，累积延迟 $\Delta T_j$ 和用于定义静默窗口的函数 $S_j(t)$ 的关键参数（如窗口的起始时间 $t_{start}$ 和持续时间 $t_{duration}$ 是梯度下降求解器的优化对象。基于PyTorch的Autograd引擎，求解器迭代地调整这些参数以最小化总损失。这个过程能有效地将不同作业的高带宽AllReduce阶段与其它作业的计算阶段错开，从而平滑聚合负载 $L(t)$。

- **评估**: 优化收敛后，系统会为每个放置候选计算一个优化分数，用于量化其争用缓解程度和对作业完成时间的预期影响。

 **评价指标**： 
#### 训练性能指标 (Training Performance Metrics)
评估通信优化对实际训练效果的影响：
##### 作业级别性能：
- Job Completion Time (JCT): 每个作业的端到端完成时间
- Training Throughput: 每秒处理的样本数或每秒完成的迭代数
- GPU Utilization/Network Utilization: 平均GPU计算利用率/网络利用率，评估是否高效率利用计算和网络，计算和网络阶段是否错开。

##### 系统级别性能：
- Makespan: 所有作业完成的总时间
##### 调度策略分析：
- Scheduling Overhead: 优化求解的计算时间开销

#### **Baseline：**
- Pollux + CASSINI 
- Pollux + Ours


#### 具体流程

- 运行 Pollux 代码，获得一组资源的 Placement；
- 替换 Pollux 在实际调度中的通信估计方案，使用我们的方法重新评估每个任务在调度间隔中的通信时间，计算平均吞吐量；
- 记录任务完成进度，系统吞吐量、网络利用率。
- 执行循环，记录任务完成时间。

