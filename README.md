# AIFlowSched

# 介绍
 这是一个专注于AI流量交错调度技术的研究与实现，提高多任务计算和通信并行执行效率。

# 问题清单
## 2025.4.17
### 代码问题
- 窗口初始化位于通信模式持续时间（Pattern.period_time）定义域之外。

### 算法问题
- 无效窗口（窗口区间被缩减到 0，不会对通信需求造成影响）长度不能按照正则化优化的预期缩减到 0。 
- 现有的窗口优化算法倾向于造成不必要的通信时延。

### 实验过程可视化
- 窗口优化过程不清晰 








# 2025-7-14

## 遗留的问题

### 多个AI任务争用同一信道的验证

### 长时间区间-多个周期-争用的验证

### 通信需求函数的其他形式的探索（非基于傅里叶变换）

### 基于任务的放置（Placement）情况，如何得到预期的通信模式

# 2025-07-19
## 当前系统存在问题
- 目标函数小并不代表带宽需求能够保持在 1 以内，可以通过可视化形象说明这个问题。