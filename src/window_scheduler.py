from typing import Dict, List, Tuple, Optional, Callable, Union

import torch

from src.communication import CommunicationPattern
from src.window_parameters import WindowParameters


class WindowScheduler:
    """
    窗口调度器类，管理多个作业的时隙窗口调度。

    提供窗口调度计算功能，并与ObjectiveFunction类紧密配合，
    支持优化计算过程中的参数设置和结果获取。
    """


    def __init__(self, bandwidth_capacity: float = 1.0, tau: float = 0.1):
        """
        初始化窗口调度器

        Args:
            bandwidth_capacity: 带宽容量上限，用于归一化带宽利用率
            tau: 时间平滑参数，控制窗口边缘平滑程度
        """
        self.jobs = {}  # 存储通信模式
        self.job_indices = {}  # 作业ID到索引的映射
        self.bandwidth_capacity = bandwidth_capacity
        self.tau = tau
        self.window_params = None  # 存储当前使用的WindowParameters对象


    def add_job(self, job_id: str, pattern: 'CommunicationPattern') -> int:
        """
        添加作业及其通信模式到调度器

        Args:
            job_id: 作业的唯一标识
            pattern: 该作业的通信模式对象

        Returns:
            int: 作业在内部数组中的索引位置
        """
        index = len(self.jobs)
        self.jobs[job_id] = pattern
        self.job_indices[job_id] = index
        return index


    def initialize_windows(self, num_windows: int, random_init: bool = True,
                           device: Optional[str] = None, transform_type: str = "softplus"):
        """
        初始化窗口参数，为每个作业分配指定数量的时隙窗口

        Args:
            num_windows: 每个作业的窗口数量
            random_init: 是否随机初始化参数，若为False则使用基于作业特征的初始化
            device: 可选的计算设备，如果为None则从作业中推断
            transform_type: 参数变换类型，支持"softplus"和"log1p"

        Returns:
            WindowParameters: 初始化后的窗口参数对象
        """
        from src.window_parameters import WindowParameters

        # 确保已添加作业
        if not self.jobs:
            raise ValueError("请先添加作业后再初始化窗口")

        # 确定计算设备
        if device is None:
            pattern = next(iter(self.jobs.values()))
            device = pattern.device if hasattr(pattern, 'device') else "cpu"

        # 创建窗口参数对象
        num_jobs = len(self.jobs)
        params = WindowParameters(num_jobs=num_jobs, num_windows=num_windows,
                                  device=device, transform_type=transform_type)

        if random_init:
            # 逐个作业进行Dirichlet初始化
            for job_idx, (job_id, pattern) in enumerate(self.jobs.items()):
                params.initialize_dirichlet_single_job(job_idx, pattern, self.tau)
        else:
            # 非随机初始化暂时保持原有逻辑
            pass

        # # 执行初始化
        # if random_init:
        #     # 随机初始化
        #     # 获取作业的时间范围用于更合理的初始化
        #     max_time = 0.0
        #     for pattern in self.jobs.values():
        #         if hasattr(pattern, 'end_time'):
        #             max_time = max(max_time, pattern.end_time)
        #         elif hasattr(pattern, 'period_length'):
        #             max_time = max(max_time, pattern.period_length)
        #
        #     max_time = max(100.0, max_time)  # 确保至少有合理的默认值
        #
        #     params.initialize_random(
        #         min_start_time=0.0,
        #         max_start_time=max_time * 0.4,  # 留出时间空间
        #         min_duration=max_time * 0.01,  # 合理的最小持续时间
        #         max_duration=max_time * 0.02  # 合理的最大持续时间
        #     )
        # else:
        #     # 基于作业特征初始化
        #     job_features = []
        #     for job_id, pattern in self.jobs.items():
        #         # 获取作业特征（如周期长度、开始时间等）
        #         start_time = getattr(pattern, 'start_time', 0.0)
        #         end_time = getattr(pattern, 'end_time', 100.0)
        #         period = getattr(pattern, 'period_length', end_time - start_time)
        #         job_features.append((start_time, period))
        #
        #     params.initialize_from_features(job_features)

        params.raw_params.requires_grad_(True)

        # 设置窗口参数
        self.window_params = params

        return params



    def set_window_params(self, params: Union[torch.Tensor, 'WindowParameters']):
        """
        设置窗口参数，支持直接传入参数张量或WindowParameters对象

        Args:
            params: 窗口参数张量或WindowParameters对象
        """
        if isinstance(params, torch.Tensor):
            if self.window_params is None:
                raise ValueError("必须先初始化WindowParameters对象")
            self.window_params.set_flat_raw_params(params)
        else:
            # WindowParameters对象
            self.window_params = params


    def get_window_params(self) -> torch.Tensor:
        """
        获取当前窗口参数的扁平化张量表示

        Returns:
            torch.Tensor: 扁平化的窗口参数张量
        """
        if self.window_params is None:
            raise ValueError("窗口参数未初始化")
        return self.window_params.get_flat_raw_params()


    def get_window_params_dict(self) -> Dict[str, List[Tuple[float, float]]]:
        """
        获取窗口参数的字典表示，兼容ObjectiveFunction的需求

        Returns:
            Dict: {job_id: [(t_1, d_1), (t_2, d_2), ...]}
        """
        if self.window_params is None:
            return {}

        transformed_params = self.window_params.get_transformed_params()
        result = {}

        for job_id, index in self.job_indices.items():
            job_windows = []
            for j in range(self.window_params.num_windows):
                t = transformed_params[index, j, 0].item()
                delta_t = transformed_params[index, j, 1].item()
                job_windows.append((t, delta_t))
            result[job_id] = job_windows

        return result


    def compute_slot_indicator(self, job_id: str, t: torch.Tensor) -> torch.Tensor:
        """
        计算时隙指示函数值，表示时刻t是否处于任何时隙窗口内

        Args:
            job_id: 作业ID
            t: 时间点或时间点张量

        Returns:
            torch.Tensor: 指示函数值，值接近0表示在窗口内，值接近1表示不在窗口内
        """
        if self.window_params is None or job_id not in self.job_indices:
            return torch.ones_like(t) if isinstance(t, torch.Tensor) else torch.tensor(1.0)

        # 获取作业索引
        job_idx = self.job_indices[job_id]

        # 获取窗口参数
        windows = self.window_params.get_transformed_params()[job_idx]

        # 确保t是张量
        if not isinstance(t, torch.Tensor):
            t = torch.tensor(t, device=self.window_params.device)

        # 计算指示函数
        indicator = torch.ones_like(t)

        for j in range(self.window_params.num_windows):
            t_start = windows[j, 0]
            delta_t = windows[j, 1]

            # 如果窗口持续时间很小，跳过
            if delta_t < 1e-6:
                continue

            # 计算窗口指示项
            left_term = 1 - torch.sigmoid((t - t_start) / self.tau)
            right_term = torch.sigmoid((t - (t_start + delta_t)) / self.tau)
            window_indicator = left_term + right_term

            # 累乘所有窗口的指示项
            indicator = indicator * window_indicator

        return indicator


    # def compute_slot_indicator(self, job_id: str, t: torch.Tensor) -> torch.Tensor:
    #     """
    #     这是一个能够并行计算各窗口的indicator的方法
    #     内存优化版本，适合大量窗口的情况
    #     """
    #     if self.window_params is None or job_id not in self.job_indices:
    #         return torch.ones_like(t) if isinstance(t, torch.Tensor) else torch.tensor(1.0)
    #
    #     job_idx = self.job_indices[job_id]
    #     windows = self.window_params.get_transformed_params()[job_idx]
    #
    #     if not isinstance(t, torch.Tensor):
    #         t = torch.tensor(t, device=self.window_params.device)
    #
    #     # 过滤有效窗口
    #     t_starts = windows[:, 0]
    #     delta_ts = windows[:, 1]
    #     # valid_mask = delta_ts > 1e-6
    #     valid_mask = delta_ts > 0
    #
    #     if not valid_mask.any():
    #         return torch.ones_like(t)
    #
    #     valid_t_starts = t_starts[valid_mask]
    #     valid_delta_ts = delta_ts[valid_mask]
    #
    #     # 分批处理，避免内存爆炸
    #     batch_size = 32  # 可调整
    #     indicator = torch.ones_like(t)
    #
    #     for i in range(0, len(valid_t_starts), batch_size):
    #         end_idx = min(i + batch_size, len(valid_t_starts))
    #         batch_t_starts = valid_t_starts[i:end_idx]
    #         batch_delta_ts = valid_delta_ts[i:end_idx]
    #
    #         # 批量计算
    #         t_expanded = t.unsqueeze(-1)
    #         t_starts_expanded = batch_t_starts.unsqueeze(0)
    #         delta_ts_expanded = batch_delta_ts.unsqueeze(0)
    #
    #         left_terms = 1 - torch.sigmoid((t_expanded - t_starts_expanded) / self.tau)
    #         right_terms = torch.sigmoid((t_expanded - (t_starts_expanded + delta_ts_expanded)) / self.tau)
    #         window_indicators = left_terms + right_terms
    #
    #         # 累乘当前批次
    #         batch_indicator = torch.prod(window_indicators, dim=-1)
    #         indicator = indicator * batch_indicator
    #
    #     return indicator

    def compute_cumulative_delay(self, job_id: str, t: torch.Tensor) -> torch.Tensor:
        """
        计算累积延迟，即到时刻t为止所有时隙窗口引入的总延迟

        Args:
            job_id: 作业ID
            t: 时间点或时间点张量

        Returns:
            torch.Tensor: 累积延迟值
        """
        if self.window_params is None or job_id not in self.job_indices:
            return torch.zeros_like(t) if isinstance(t, torch.Tensor) else torch.tensor(0.0)

        # 获取作业索引
        job_idx = self.job_indices[job_id]

        # 获取窗口参数
        windows = self.window_params.get_transformed_params()[job_idx] # 目前是[5,2]的格式 [start_time, duration_time]

        # 确保t是张量
        if not isinstance(t, torch.Tensor):
            t = torch.tensor(t, device=self.window_params.device)

        # 计算累积延迟
        cumulative_delay = torch.zeros_like(t)

        for j in range(self.window_params.num_windows):
            t_start = windows[j, 0]
            delta_t = windows[j, 1]

            # 如果窗口持续时间很小，跳过
            if delta_t < 1e-6:
                continue
            # 计算 \delta_T
            # 完全通过窗口的延迟
            complete_delay = delta_t * torch.sigmoid((t - (t_start + delta_t)) / self.tau)

            # 正在通过窗口的部分延迟
            in_window = torch.sigmoid((t - t_start) / self.tau) - torch.sigmoid((t - (t_start + delta_t)) / self.tau)
            partial_delay = (t - t_start) * in_window

            # 累加延迟
            cumulative_delay = cumulative_delay + complete_delay + partial_delay

        return cumulative_delay


    # def compute_cumulative_delay(self, job_id: str, t: torch.Tensor) -> torch.Tensor:
    #     """
    #     计算累积延迟，即到时刻t为止所有时隙窗口引入的总延迟
    #     """
    #     if self.window_params is None or job_id not in self.job_indices:
    #         return torch.zeros_like(t) if isinstance(t, torch.Tensor) else torch.tensor(0.0)
    #
    #     job_idx = self.job_indices[job_id]
    #     windows = self.window_params.get_transformed_params()[job_idx]
    #
    #     if not isinstance(t, torch.Tensor):
    #         t = torch.tensor(t, device=self.window_params.device)
    #
    #     # 过滤有效窗口
    #     t_starts = windows[:, 0]  # [num_windows]
    #     delta_ts = windows[:, 1]  # [num_windows]
    #     valid_mask = delta_ts > 0
    #     # valid_mask = delta_ts > 1e-6
    #     # valid_mask = True
    #
    #     if not valid_mask.any():
    #         return torch.zeros_like(t)
    #
    #     valid_t_starts = t_starts[valid_mask]  # [num_valid_windows]
    #     valid_delta_ts = delta_ts[valid_mask]  # [num_valid_windows]
    #
    #     # 广播计算
    #     t_expanded = t.unsqueeze(-1)  # [batch_size, 1]
    #     t_starts_expanded = valid_t_starts.unsqueeze(0)  # [1, num_valid_windows]
    #     delta_ts_expanded = valid_delta_ts.unsqueeze(0)  # [1, num_valid_windows]
    #     t_ends_expanded = t_starts_expanded + delta_ts_expanded  # [1, num_valid_windows]
    #
    #     # 并行计算所有窗口的延迟 [batch_size, num_valid_windows]
    #     # 完全通过窗口的延迟
    #     complete_delays = delta_ts_expanded * torch.sigmoid((t_expanded - t_ends_expanded) / self.tau)
    #
    #     # 正在通过窗口的部分延迟
    #     in_window = (torch.sigmoid((t_expanded - t_starts_expanded) / self.tau) -
    #                  torch.sigmoid((t_expanded - t_ends_expanded) / self.tau))
    #     partial_delays = (t_expanded - t_starts_expanded) * in_window
    #
    #     # 所有窗口的总延迟
    #     total_delays = complete_delays + partial_delays  # [batch_size, num_valid_windows]
    #
    #     # 沿窗口维度求和
    #     cumulative_delay = torch.sum(total_delays, dim=-1)  # [batch_size]
    #
    #     return cumulative_delay

    def get_scheduled_demand(self, job_id: str, t: torch.Tensor) -> torch.Tensor:
        """
        计算特定作业在调度后的时间点t的通信需求

        Args:
            job_id: 作业ID
            t: 时间点或时间点张量

        Returns:
            torch.Tensor: 调度后的通信需求值
        """
        if job_id not in self.jobs:
            return torch.zeros_like(t) if isinstance(t, torch.Tensor) else torch.tensor(0.0)

        # 计算延迟和指示函数
        delay = self.compute_cumulative_delay(job_id, t)
        indicator = self.compute_slot_indicator(job_id, t)

        # 获取通信模式
        pattern = self.jobs[job_id]

        # 使用延迟后的时间计算原始需求
        if isinstance(t, torch.Tensor) and t.dim() > 0:
            original_demand = pattern.evaluate_batch(t - delay,True)
        else:
            original_demand = pattern.evaluate_at_time(t - delay)

        # 应用时隙指示函数
        scheduled_demand = original_demand * indicator

        return scheduled_demand


    def get_total_demand(self, t: torch.Tensor) -> torch.Tensor:
        """
        计算所有作业在时间点t的总通信需求

        Args:
            t: 时间点或时间点张量

        Returns:
            torch.Tensor: 总通信需求值
        """
        # 确保t是张量
        if not isinstance(t, torch.Tensor):
            device = self.window_params.device if self.window_params else torch.device('cpu')
            t = torch.tensor(t, device=device)

        # 初始化总需求
        total_demand = torch.zeros_like(t)

        # 累加所有作业的需求
        for job_id in self.jobs:
            job_demand = self.get_scheduled_demand(job_id, t)
            total_demand = total_demand + job_demand

        return total_demand


    def get_normalized_load(self, t: torch.Tensor) -> torch.Tensor:
        """
        计算时间点t的归一化带宽利用率

        Args:
            t: 时间点或时间点张量

        Returns:
            torch.Tensor: 归一化带宽利用率
        """
        total_demand = self.get_total_demand(t)
        return total_demand / self.bandwidth_capacity


    @staticmethod
    def batch_compute(func: Callable, time_points: torch.Tensor) -> torch.Tensor:
        """
        批量计算指定函数在多个时间点的值

        Args:
            func: 接受时间参数的函数
            time_points: 时间点张量

        Returns:
            torch.Tensor: 批量计算结果
        """
        # 直接调用函数，支持批量计算
        return func(time_points)