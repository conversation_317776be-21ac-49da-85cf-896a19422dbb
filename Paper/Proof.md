

**理论分析与优化必要性证明**

#### 为什么在 $K < \lfloor N/2 \rfloor$ 时必须采用优化方法？我们可以通过最小二乘原理进行严格证明。

考虑傅里叶基函数：
$$\phi_0(t) = 1$$
$$\phi_{2k-1}(t) = \cos\left(\frac{2\pi k t}{T}\right), \quad k = 1,2,\ldots,K$$
$$\phi_{2k}(t) = \sin\left(\frac{2\pi k t}{T}\right), \quad k = 1,2,\ldots,K$$

当 $K = \lfloor N/2 \rfloor$ 时，这组基函数在采样点集 $\{t_i\}_{i=0}^{N-1}$ 上构成一个完备的正交基，任何离散采样序列都可以被精确表示。
然而，当 $K < \lfloor N/2 \rfloor$ 时，我们实际上在求解一个过约束系统（方程数 $N$ 大于未知数 $2K+1$）。此时，最小二乘解为：

$$\mathbf{c} = (\mathbf{\Phi}^T\mathbf{\Phi})^{-1}\mathbf{\Phi}^T\mathbf{f}$$

其中，$\mathbf{c} = [a_0/2, a_1, b_1, \ldots, a_K, b_K]^T$ 为系数向量，$\mathbf{\Phi}$ 为设计矩阵，其元素 $\Phi_{ij} = \phi_j(t_i)$，$\mathbf{f} = [f(t_0), f(t_1), \ldots, f(t_{N-1})]^T$ 为原始数据向量。

对于非均匀采样或添加窗口函数等情况，设计矩阵的正交性不再保持，求解过程变得更加复杂，必须诉诸于数值优化方法。




## 度量函数 F(L) 的数学性质分析

本文档对度量函数进行完整、详细的数学分析。该函数设计用于分布式深度学习通信调度中的带宽争用度量，旨在引导优化过程将带宽利用率收敛到理想值 $1-\mu$，同时在低利用率时鼓励资源使用，在高利用率时施加强力惩罚。

### 函数定义

度量函数为：
$$
F(L) = \alpha (L-(1-\mu))^2 + \beta \left[ e^{\gamma(L-(1-\mu))} - 1 - \gamma (L-(1-\mu)) \right]
$$
其中：
- $L = G(t)/B$ 为归一化带宽利用率（$G(t)$ 为总通信需求，$B$ 为带宽上限）。
- 参数：$\alpha > 0$（控制二次惩罚强度）、$\beta > 0$（控制指数惩罚强度）、$\gamma > 0$（控制指数增长速率）、$\mu \in (0,1)$（安全边界比例，例如 $\mu=0.05$ 表示目标利用率为 95%）。

此函数确保在 $L=1-\mu$ 处取得唯一全局最小值，并保持严格凸性。

### 函数的导数

#### 一阶导数
函数的一阶导数为：
$$
F'(L) = 2\alpha(L-(1-\mu)) + \beta\gamma \left[ e^{\gamma(L-(1-\mu))} - 1 \right]
$$
- 该导数在整个定义域上连续，体现了函数的可微性。

#### 二阶导数
函数的二阶导数为：
$$
F''(L) = 2\alpha + \beta\gamma^2 e^{\gamma(L-(1-\mu))} > 0
$$
- 二阶导数恒正，确保函数的严格凸性（详见下文证明）。

### 函数的凹凸性

**定理：** 函数 $F(L)$ 是关于 $L$ 的严格凸函数。

**证明：**  
给定参数 $\alpha > 0$、$\beta > 0$、$\gamma > 0$，二阶导数为：
$$
F''(L) = 2\alpha + \beta\gamma^2 e^{\gamma(L-(1-\mu))} 
$$
指数项 $e^{\gamma(L-(1-\mu))} \geq e^{-\gamma(1-\mu)} > 0$（因为 $\mu < 1$），因此：
$$
F''(L) \geq 2\alpha + \beta\gamma^2 e^{-\gamma(1-\mu)} > 0
$$
二阶导数在整个实数域上严格大于零，故 $F(L)$ 为严格凸函数。严格凸函数的任意局部最小值均为全局最小值，且全局最小值点唯一（如果存在）。 $\blacksquare$

**推论：** 由于严格凸性，$F(L)$ 仅存在唯一的全局最小值点，且优化算法（如梯度下降）将收敛到该点。

### 函数的最小值点

**定理：** $L = 1-\mu$ 是函数 $F(L)$ 的唯一全局最小值点，且最小值为 0。

**证明：**  
1. **临界点计算：** 设 $F'(L) = 0$：
   $$
   2\alpha(L-(1-\mu)) + \beta\gamma \left[ e^{\gamma(L-(1-\mu))} - 1 \right] = 0
   $$
   令 $x = L - (1-\mu)$，则方程简化为：
   $$
   2\alpha x + \beta\gamma (e^{\gamma x} - 1) = 0
   $$
   当 $x=0$（即 $L=1-\mu$）时：
   $$
   2\alpha \cdot 0 + \beta\gamma (e^0 - 1) = 0
   $$
   故 $L=1-\mu$ 是临界点。

2. **唯一性：** 假设存在另一个临界点 $L' \neq 1-\mu$（$x' \neq 0$）。由严格凸性，$F'(L)$ 是严格递增的（因为 $F''(L)>0$），因此方程 $F'(L)=0$ 最多有一个解。矛盾，故唯一。

3. **最小值性质：** 结合 $F''(L)>0$，该临界点为最小值点。函数值：
   $$
   F(1-\mu) = \alpha \cdot 0^2 + \beta \left[ e^0 - 1 - \gamma \cdot 0 \right] = \beta(1-1) = 0
   $$

因此，$L = 1-\mu$ 是唯一全局最小值点。 $\blacksquare$

**实际含义：** 在优化过程中，系统的带宽利用率将自然收敛至 $1-\mu$ 附近，实现高效利用而不易过载。

### 函数的单调性

函数 $F(L)$ 在不同区间表现出不同的单调性。我们将定义域分为两个主要区间（基于最小值点），并进一步细分高利用率区间。分析基于典型参数（如 $\mu=0.05$、$\gamma=10$）进行定量说明。

#### 区间 I：$L < 1-\mu$ (带宽利用率低于目标值)
- **单调性：** $F'(L) < 0$，函数严格单调递减。
- **证明：** 当 $L < 1-\mu$ 时，令 $x = L - (1-\mu) < 0$：
  - $e^{\gamma x} < 1$，故 $e^{\gamma x} - 1 < 0$。
  - 第一项 $2\alpha x < 0$，第二项 $\beta\gamma (e^{\gamma x} - 1) < 0$。
  - 因此 $F'(L) < 0$。

- **在 $L=0$ 处的函数值（无通信负载时）：**
  $$
  \begin{aligned}
  F(0) &= \alpha(0 - (1-\mu))^2 + \beta \left[ e^{\gamma(0-(1-\mu))} - 1 - \gamma(0-(1-\mu)) \right] \\
       &= \alpha(1-\mu)^2 + \beta \left[ e^{-\gamma(1-\mu)} - 1 + \gamma(1-\mu) \right]
  \end{aligned}
  $$
  对于典型参数（如 $\mu=0.05$、$\gamma=10$），$e^{-\gamma(1-\mu)} \approx e^{-9.5} \approx 7.4 \times 10^{-5}$（很小），因此：
  $$
  F(0) \approx \alpha(0.95)^2 + \beta \left[ 0 - 1 + 9.5 \right] = 0.9025\alpha + \beta \cdot 8.5
  $$
  该值远大于最小值 $F(1-\mu)=0$，体现了对带宽资源闲置的显著惩罚。

- **在 $L=0$ 处的导数值：**
  $$
  F'(0) = 2\alpha(0-(1-\mu)) + \beta\gamma \left[ e^{-\gamma(1-\mu)} - 1 \right] \approx -2\alpha(0.95) + \beta\gamma(-1) < 0
  $$

- **数学含义：** 随着带宽利用率从零增加接近目标值 $1-\mu$，损失函数值严格递减，鼓励低负载下的通信合并和带宽充分利用。下降速率由 $\alpha$ 和 $\beta\gamma$ 控制，近似线性（指数项贡献小）。

#### 区间 II：$L > 1-\mu$ (带宽利用率高于目标值)
- **单调性：** $F'(L) > 0$，函数严格单调递增。
- **证明：** 当 $L > 1-\mu$ 时，$x > 0$：
  - $e^{\gamma x} > 1$，故 $e^{\gamma x} - 1 > 0$。
  - 第一项 $2\alpha x > 0$，第二项 $\beta\gamma (e^{\gamma x} - 1) > 0$。
  - 因此 $F'(L) > 0$。

##### 子区间 IIa：$1-\mu < L < 1$ (带宽利用率处于过渡区间)
- **增长温和**：指数项 $e^{\gamma x}$ 以中等速率增长（$x < \mu$ 小）。
- **在 $L=1$ 处的函数值（刚好达到上限时）：**
  $$
  \begin{aligned}
  F(1) &= \alpha(1-(1-\mu))^2 + \beta \left[ e^{\gamma(1-(1-\mu))} - 1 - \gamma(1-(1-\mu)) \right] \\
       &= \alpha\mu^2 + \beta \left[ e^{\gamma\mu} - 1 - \gamma\mu \right]
  \end{aligned}
  $$
  对于 $\mu=0.05$、$\gamma=10$：
  $$
  e^{\gamma\mu} \approx e^{0.5} \approx 1.65, \quad F(1) \approx 0.0025\alpha + \beta(1.65 - 1 - 0.5) = 0.0025\alpha + 0.15\beta
  $$

- **在 $L=1$ 处的导数值：**
  $$
  F'(1) = 2\alpha\mu + \beta\gamma \left[ e^{\gamma\mu} - 1 \right] \approx 0.1\alpha + 10\beta(0.65) = 0.1\alpha + 6.5\beta
  $$

- **数学含义：** 带宽利用率超过理想值但仍未超限时，代价开始温和增加，体现了系统对轻微超出理想值的容忍。增长率由 $\alpha$ 主导，指数项提供渐进式加速。

##### 子区间 IIb：$L \geq 1$ (带宽利用率超过上限)
- **增长快速**：指数项主导，函数值和导数急剧增大。
- **在超出上限 $\varepsilon$ 处 ($L = 1+\varepsilon$)：**
  $$
  \begin{aligned}
  F(1+\varepsilon) &= \alpha((1+\varepsilon)-(1-\mu))^2 + \beta \left[ e^{\gamma((1+\varepsilon)-(1-\mu))} - 1 - \gamma((1+\varepsilon)-(1-\mu)) \right] \\
                   &= \alpha(\mu+\varepsilon)^2 + \beta \left[ e^{\gamma(\mu+\varepsilon)} - 1 - \gamma(\mu+\varepsilon) \right]
  \end{aligned}
  $$
  对于 $\gamma=10$、$\varepsilon=0.2$：
  $$
  e^{\gamma(\mu+0.2)} \approx e^{2.5} \approx 12.18, \quad F(1.2) \approx \alpha(0.25)^2 + \beta(12.18 - 1 - 2.5) \approx 0.0625\alpha + 8.68\beta
  $$
  惩罚显著增大。

- **在 $L=1+\varepsilon$ 处的导数值：**
  $$
  F'(1+\varepsilon) = 2\alpha(\mu+\varepsilon) + \beta\gamma \left[ e^{\gamma(\mu+\varepsilon)} - 1 \right] \gg 0
  $$
  指数项主导快速增长。

- **数学含义：** 超过带宽上限后，代价以指数级增长，形成强烈的"惩罚"效应，有效防止系统长时间处于争用状态。惩罚速率由 $\beta\gamma$ 控制，确保对严重过载的敏感响应。

### 总体分析与实际含义

通过上述分析，我们可以定量地理解函数 $F(L)$ 如何在不同区间实现不同目标：
- **低利用率区间 (I)**：促进资源使用，损失递减。
- **理想区间附近**：达到最优平衡（最小值 0）。
- **超出上限区间 (IIb)**：施加强力指数惩罚。

这种数学特性精确契合通信调度的目标要求：鼓励高效利用带宽、容忍轻微波动、严厉惩罚争用。函数的全域可微性和严格凸性确保了基于梯度的优化算法（如 Adam 或 L-BFGS）的稳定收敛。

#### 参数调优建议
- **$\mu$**：设置目标利用率（e.g., 0.05-0.1）。
- **$\alpha$**：控制整体二次惩罚（较大值使曲线更陡峭）。
- **$\beta, \gamma$**：控制指数惩罚（$\gamma$ 越大，超载惩罚越剧烈）。
- **数值实验**：在实际优化中，通过网格搜索调参，确保函数行为符合系统需求。

此分析基于严格数学证明，提供了一个可靠的理论基础。

