import torch


class ContentionMetrics:
    """
    通信争用度量计算类，提供可微分的争用度量函数。

    该类负责量化网络资源争用程度，是构建优化目标的关键组件。
    实现了基于指数惩罚的度量函数，在达到目标带宽利用率时取最小值，
    超过带宽上限时快速增长。
    """


    def __init__(self,
                 alpha: float = 1.0,
                 beta: float = 0.2,
                 gamma: float = 10.0,
                 mu: float = 0.05):
        """
        初始化通信争用度量计算器

        Args:
            alpha: 抛物线项权重，控制调度目标带宽利用率
            beta: 指数项权重，控制对超出带宽的惩罚强度
            gamma: 指数增长率，控制惩罚增长的陡峭程度
            mu: 安全边界比例，确定目标带宽利用率
        """
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.mu = mu

    def pointwise_metric_batch(self, loads: torch.Tensor) -> torch.Tensor:
        """
        批量计算争用度量，支持向量化操作

        F(L) = α(L-(1-μ))² + β[e^(γ(L-(1-μ))) - 1 - γ(L-(1-μ))]

        Args:
            loads: 形状为 [batch_size] 的归一化带宽利用率张量

        Returns:
            torch.Tensor: 形状为 [batch_size] 的争用度量值张量
        """
        # 目标带宽利用率
        target_load = 1.0 - self.mu

        # 计算偏差项 x = L - (1-μ)
        deviation = loads - target_load

        # 抛物线项：α(L - (1-μ))²
        quadratic_term = self.alpha * deviation ** 2

        # 指数项：β[e^(γ(L-(1-μ))) - 1 - γ(L-(1-μ))]
        # 分别计算三个部分
        exp_component = torch.exp(self.gamma * deviation)  # e^(γ(L-(1-μ)))
        constant_component = torch.ones_like(deviation)  # 1
        linear_component = self.gamma * deviation  # γ(L-(1-μ))

        # 组合指数项
        exp_term = self.beta * (exp_component - constant_component - linear_component)

        # 总度量值
        metric = quadratic_term + exp_term

        return metric