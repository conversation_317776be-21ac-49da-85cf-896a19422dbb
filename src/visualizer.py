
import numpy as np
import torch
import matplotlib.pyplot as plt
from matplotlib.gridspec import GridSpec


class EnhancedScheduleVisualizer:
    """
    增强型调度可视化器，特别处理不同任务周期和窗口引起的时延
    """

    def __init__(self, scheduler, time_resolution=1000):
        self.scheduler = scheduler
        self.time_resolution = time_resolution
        self.device = scheduler.window_params.device if scheduler.window_params else torch.device('cpu')

    def visualize_jobs(self, job_ids=None, figsize=(16, 12), output_path=None, dpi=300):
        """
        主要可视化函数，绘制DFT拟合和窗口调度对比图
        """
        # 如果未指定作业ID，获取所有作业
        if job_ids is None:
            job_ids = list(self.scheduler.jobs.keys())
            if len(job_ids) > 3:  # 限制默认显示的作业数量
                job_ids = job_ids[:3]

                # 创建图形
        fig = plt.figure(figsize=figsize)
        gs = GridSpec(len(job_ids) * 2, 1)

        # 为每个作业选择颜色
        colors = plt.cm.tab10(np.linspace(0, 1, len(job_ids)))

        # 为每个作业创建两个子图
        for i, job_id in enumerate(job_ids):
            # 获取当前作业的通信模式
            pattern = self.scheduler.jobs[job_id]

            # 计算该作业的DFT拟合通信需求
            original_start = pattern.start_time
            original_end = pattern.end_time
            dft_times = torch.linspace(original_start, original_end, self.time_resolution, device=self.device)
            dft_demand = pattern.evaluate_batch(dft_times, True)  # True表示使用DFT拟合

            # 计算该作业的窗口调度后通信需求
            # 首先确定调度后的时间范围
            max_window_end = original_end
            job_idx = self.scheduler.job_indices[job_id]
            if self.scheduler.window_params:
                windows = self.scheduler.window_params.get_transformed_params()[job_idx]
                for window in windows:
                    window_end = window[0].item() + window[1].item()
                    max_window_end = max(max_window_end, window_end)

                    # 增加一些缓冲区以显示全部窗口
            max_window_end += (max_window_end - original_start) * 0.1

            # 创建调度时间点
            scheduled_times = torch.linspace(original_start, max_window_end, self.time_resolution, device=self.device)
            scheduled_demand = self.scheduler.get_scheduled_demand(job_id, scheduled_times)

            # 获取窗口信息
            windows = []
            if self.scheduler.window_params:
                window_params = self.scheduler.window_params.get_transformed_params()[job_idx]
                windows = [(w[0].item(), w[1].item()) for w in window_params]

                # 1. DFT拟合需求子图
            ax1 = fig.add_subplot(gs[i * 2])
            ax1.set_title(f"{job_id}: DFT Fitted Communication Demand", fontsize=12)
            ax1.set_ylabel("Bandwidth Demand", fontsize=10)

            # 绘制DFT拟合需求曲线 - 修复：添加detach()
            ax1.plot(dft_times.detach().cpu().numpy(), dft_demand.detach().cpu().numpy(),
                     color=colors[i], linewidth=2)

            # 添加带宽容量线
            ax1.axhline(y=self.scheduler.bandwidth_capacity, color='r', linestyle='--',
                        label=f"Bandwidth Capacity ({self.scheduler.bandwidth_capacity})")

            ax1.grid(True, alpha=0.3)
            ax1.legend(loc='upper right')

            # 2. 调度后需求子图
            ax2 = fig.add_subplot(gs[i * 2 + 1])
            ax2.set_title(f"{job_id}: Scheduled Communication Demand with Windows", fontsize=12)
            ax2.set_ylabel("Bandwidth Demand", fontsize=10)

            # 绘制调度后需求曲线
            ax2.plot(scheduled_times.detach().cpu().numpy(), scheduled_demand.detach().cpu().numpy(),
                     color=colors[i], linewidth=2)

            # 标注窗口
            max_demand = scheduled_demand.detach().max().item() * 1.1
            for idx, (start, duration) in enumerate(windows):
                # 绘制窗口背景
                ax2.axvspan(start, start + duration, alpha=0.2, color=colors[i])

                # 窗口边界线
                ax2.axvline(x=start, color='green', linestyle='-', alpha=0.5)
                ax2.axvline(x=start + duration, color='red', linestyle='-', alpha=0.5)

                # 窗口标签
                window_height = max_demand * (1.0 + 0.05 * (idx % 3))  # 错开标签
                ax2.text(start + duration / 2, window_height,
                         f"Window {idx + 1}\n({duration:.1f})",
                         ha='center', va='bottom', fontsize=8,
                         bbox=dict(facecolor='white', alpha=0.7))

                # 添加带宽容量线
            ax2.axhline(y=self.scheduler.bandwidth_capacity, color='r', linestyle='--',
                        label=f"Bandwidth Capacity ({self.scheduler.bandwidth_capacity})")

            ax2.grid(True, alpha=0.3)
            ax2.legend(loc='upper right')

            # 只在最后一个子图添加x轴标签
            if i == len(job_ids) - 1:
                ax2.set_xlabel("Time", fontsize=12)

                # 调整布局
        plt.tight_layout()

        # 保存图表
        if output_path:
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight')
            print(f"调度可视化已保存到: {output_path}")

        return fig

    def visualize_combined(self, job_ids=None, figsize=(16, 12), output_path=None, dpi=300):
        """
        绘制所有作业的组合可视化
        """
        # 如果未指定作业ID，获取所有作业
        if job_ids is None:
            job_ids = list(self.scheduler.jobs.keys())

            # 创建图形
        fig = plt.figure(figsize=figsize)
        gs = GridSpec(2, 1, height_ratios=[1, 1.5])

        # 为每个作业选择颜色
        colors = plt.cm.tab10(np.linspace(0, 1, len(job_ids)))

        # 1. DFT拟合需求（上图）
        ax1 = fig.add_subplot(gs[0])
        ax1.set_title("DFT Fitted Communication Demands (All Jobs)", fontsize=14)
        ax1.set_ylabel("Bandwidth Demand", fontsize=12)

        # 找出所有作业的最大原始时间范围
        max_original_time = 0
        min_original_time = float('inf')
        for job_id in job_ids:
            pattern = self.scheduler.jobs[job_id]
            min_original_time = min(min_original_time, pattern.start_time)
            max_original_time = max(max_original_time, pattern.end_time)

            # 创建共享的原始时间点
        original_times = torch.linspace(min_original_time, max_original_time,
                                        self.time_resolution, device=self.device)

        # 计算并绘制每个作业的DFT拟合需求
        total_dft_demand = torch.zeros_like(original_times)
        for i, job_id in enumerate(job_ids):
            pattern = self.scheduler.jobs[job_id]

            # 使用作业自身的时间范围计算DFT需求
            job_start = pattern.start_time
            job_end = pattern.end_time
            job_times = torch.linspace(job_start, job_end, self.time_resolution, device=self.device)
            job_demand = pattern.evaluate_batch(job_times, True)  # True表示使用DFT拟合

            # 将作业需求插值到共享时间轴
            interpolated_demand = torch.zeros_like(original_times)
            mask = (original_times >= job_start) & (original_times <= job_end)
            if mask.any():
                valid_times = original_times[mask]
                # 线性插值
                indices = (valid_times - job_start) / (job_end - job_start) * (len(job_demand) - 1)
                indices = indices.clamp(0, len(job_demand) - 1)
                indices_floor = indices.floor().long()
                indices_ceil = indices.ceil().long().clamp(max=len(job_demand) - 1)
                weights_ceil = indices - indices_floor
                weights_floor = 1.0 - weights_ceil

                interpolated_valid = job_demand[indices_floor] * weights_floor + job_demand[indices_ceil] * weights_ceil
                interpolated_demand[mask] = interpolated_valid

                # 绘制该作业的DFT需求 - 修复：添加detach()
            ax1.plot(original_times.detach().cpu().numpy(), interpolated_demand.detach().cpu().numpy(),
                     label=f"{job_id} DFT", color=colors[i], linewidth=2)

            # 累加总需求
            total_dft_demand += interpolated_demand

            # 绘制DFT总需求 - 修复：添加detach()
        # ax1.plot(original_times.detach().cpu().numpy(), total_dft_demand.detach().cpu().numpy(),
        #          label="Total DFT Demand", color='k', linewidth=2, linestyle='-')

        # 添加容量线
        ax1.axhline(y=self.scheduler.bandwidth_capacity, color='r', linestyle='--',
                    label=f"Bandwidth Capacity ({self.scheduler.bandwidth_capacity})")

        # 计算并显示DFT超出容量部分 - 修复：添加detach()
        above_capacity = total_dft_demand > self.scheduler.bandwidth_capacity
        # if above_capacity.any():
        #     ax1.fill_between(original_times.detach().cpu().numpy(), total_dft_demand.detach().cpu().numpy(),
        #                      self.scheduler.bandwidth_capacity, where=above_capacity.detach().cpu().numpy(),
        #                      color='red', alpha=0.3, label='Over Capacity')
        #
        # ax1.grid(True, alpha=0.3)
        # ax1.legend(loc='upper right')

        # 2. 调度后需求（下图）
        ax2 = fig.add_subplot(gs[1])
        ax2.set_title("Scheduled Communication Demands with Windows (All Jobs)", fontsize=14)
        ax2.set_ylabel("Bandwidth Demand", fontsize=12)
        ax2.set_xlabel("Time", fontsize=12)

        # 找出所有窗口的最大结束时间
        max_scheduled_time = max_original_time
        for job_id in job_ids:
            job_idx = self.scheduler.job_indices[job_id]
            if self.scheduler.window_params:
                windows = self.scheduler.window_params.get_transformed_params()[job_idx] # 若干个窗口时间组
                for window in windows:
                    window_end = window[0].item() + window[1].item()
                    max_scheduled_time = max(max_scheduled_time, window_end)

                    # 创建调度时间点
        scheduled_times = torch.linspace(min_original_time, max_scheduled_time,
                                         self.time_resolution, device=self.device)

        # 计算每个时间点的总调度需求
        total_scheduled_demand = torch.zeros_like(scheduled_times)
        for t_idx, t in enumerate(scheduled_times):
            for job_id in job_ids:
                demand = self.scheduler.get_scheduled_demand(job_id, t.unsqueeze(0)).item()
                total_scheduled_demand[t_idx] += demand

                # 绘制每个作业的调度需求和窗口
        for i, job_id in enumerate(job_ids):
            # 计算该作业的调度需求
            job_scheduled_demand = torch.zeros_like(scheduled_times)
            for t_idx, t in enumerate(scheduled_times):
                job_scheduled_demand[t_idx] = self.scheduler.get_scheduled_demand(job_id, t.unsqueeze(0)).item()

                # 绘制调度需求 - 修复：添加detach()
            ax2.plot(scheduled_times.detach().cpu().numpy(), job_scheduled_demand.detach().cpu().numpy(),
                     label=f"{job_id} Scheduled", color=colors[i], linewidth=2)

            # 标注窗口
            job_idx = self.scheduler.job_indices[job_id]
            if self.scheduler.window_params:
                windows = self.scheduler.window_params.get_transformed_params()[job_idx]
                for idx, window in enumerate(windows):
                    start = window[0].item()
                    duration = window[1].item()
                    # 窗口背景
                    ax2.axvspan(start, start + duration, alpha=0.2, color=colors[i])
                    # 边界线
                    ax2.axvline(x=start, color='green', linestyle='-', alpha=0.5)
                    ax2.axvline(x=start + duration, color='red', linestyle='-', alpha=0.5)
                    # 标签
                    max_demand = job_scheduled_demand.detach().max().item()
                    label_height = max_demand * (1.1 + 0.1 * (idx % 3))
                    ax2.text(start + duration / 2, label_height,
                             f"{job_id} Win{idx + 1}\n({duration:.1f})",
                             ha='center', va='bottom', fontsize=8,
                             bbox=dict(facecolor='white', alpha=0.7))

                    # 绘制总调度需求 - 修复：添加detach()
        # ax2.plot(scheduled_times.detach().cpu().numpy(), total_scheduled_demand.detach().cpu().numpy(),
        #          label="Total Scheduled", color='k', linewidth=2)

        # 添加容量线
        # ax2.axhline(y=self.scheduler.bandwidth_capacity, color='r', linestyle='--',
        #             label=f"Bandwidth Capacity ({self.scheduler.bandwidth_capacity})")

        # 计算并显示调度后超出容量部分 - 修复：添加detach()
        # above_capacity = total_scheduled_demand > self.scheduler.bandwidth_capacity
        # if above_capacity.any():
        #     ax2.fill_between(scheduled_times.detach().cpu().numpy(), total_scheduled_demand.detach().cpu().numpy(),
        #                      self.scheduler.bandwidth_capacity, where=above_capacity.detach().cpu().numpy(),
        #                      color='red', alpha=0.3, label='Over Capacity')
        #
        #     # 计算统计信息
        #     overload_ratio = torch.mean(above_capacity.float()).item()
        #     mean_overload = torch.mean(
        #         (total_scheduled_demand[above_capacity] - self.scheduler.bandwidth_capacity)).item()
        #
        #     # 添加超出信息
        #     ax2.text(0.02, 0.98,
        #              f"Overload: {overload_ratio:.1%}\nMean: {mean_overload:.2f}",
        #              transform=ax2.transAxes, va='top',
        #              bbox=dict(facecolor='white', alpha=0.7))
        #
        ax2.grid(True, alpha=0.3)
        ax2.legend(loc='upper right')

        # 调整布局
        plt.tight_layout()

        # 保存图表
        if output_path:
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight')
            print(f"组合调度可视化已保存到: {output_path}")

        return fig

    def visualize_scheduled_demands_only(self, job_ids=None, figsize=(16, 8), output_path=None, dpi=300):
        """
        Visualize only the scheduled communication demands for all jobs without window annotations.
        This method focuses solely on the bandwidth demand curves over time after scheduling.
        
        Args:
            job_ids: List of job IDs to visualize. If None, all jobs are included.
            figsize: Figure size tuple (width, height)
            output_path: Output path for saving the visualization
            dpi: Resolution for saving the image
        
        Returns:
            matplotlib.figure.Figure: The created figure object
        """
        # Get all job IDs if not specified
        if job_ids is None:
            job_ids = list(self.scheduler.jobs.keys())
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize)
        
        # Generate improved colors for each job - consistent with DFT visualization
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                  '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        if len(job_ids) > len(colors):
            colors = plt.cm.Set1(np.linspace(0, 1, len(job_ids)))
        
        # Find the time range for all jobs
        max_original_time = 0
        min_original_time = float('inf')
        for job_id in job_ids:
            pattern = self.scheduler.jobs[job_id]
            min_original_time = min(min_original_time, pattern.start_time)
            max_original_time = max(max_original_time, pattern.end_time)
        
        # Find the maximum end time considering all windows
        max_scheduled_time = max_original_time
        for job_id in job_ids:
            job_idx = self.scheduler.job_indices[job_id]
            if self.scheduler.window_params:
                windows = self.scheduler.window_params.get_transformed_params()[job_idx]
                for window in windows:
                    window_end = window[0].item() + window[1].item()
                    max_scheduled_time = max(max_scheduled_time, window_end)
        
        # Create time points for evaluation
        scheduled_times = torch.linspace(min_original_time, max_scheduled_time,
                                       self.time_resolution, device=self.device)
        
        # Calculate and plot individual job scheduled demands
        total_scheduled_demand = torch.zeros_like(scheduled_times)
        
        for i, job_id in enumerate(job_ids):
            # Calculate scheduled demand for this job
            job_scheduled_demand = torch.zeros_like(scheduled_times)
            for t_idx, t in enumerate(scheduled_times):
                job_scheduled_demand[t_idx] = self.scheduler.get_scheduled_demand(job_id, t.unsqueeze(0)).item()
            
            # Plot individual job demand curve with improved styling
            color = colors[i] if i < len(colors) else colors[i % len(colors)]
            ax.plot(scheduled_times.detach().cpu().numpy(), 
                   job_scheduled_demand.detach().cpu().numpy(),
                   label=f"{job_id} Scheduled", 
                   color=color, 
                   linewidth=2.5,
                   alpha=0.8)
            
            # Accumulate total demand
            total_scheduled_demand += job_scheduled_demand
        
        # Plot total scheduled demand with emphasis
        ax.plot(scheduled_times.detach().cpu().numpy(), 
               total_scheduled_demand.detach().cpu().numpy(),
               label="Total Scheduled Demand", 
               color='black', 
               linewidth=4,
               linestyle='-',
               alpha=0.9)
        
        # Add bandwidth capacity line as reference
        ax.axhline(y=self.scheduler.bandwidth_capacity, 
                  color='red', 
                  linestyle='--',
                  linewidth=3,
                  alpha=0.8,
                  label=f"Bandwidth Capacity ({self.scheduler.bandwidth_capacity})")
        
        # Highlight over-capacity regions
        above_capacity = total_scheduled_demand > self.scheduler.bandwidth_capacity
        if above_capacity.any():
            ax.fill_between(scheduled_times.detach().cpu().numpy(), 
                           total_scheduled_demand.detach().cpu().numpy(),
                           self.scheduler.bandwidth_capacity, 
                           where=above_capacity.detach().cpu().numpy(),
                           color='red', 
                           alpha=0.2, 
                           label='Over Capacity')
        
        # Set labels and title
        ax.set_title("Scheduled Communication Demands - All Jobs (After Window Insertion)", fontsize=16, fontweight='bold')
        ax.set_xlabel("Time", fontsize=14)
        ax.set_ylabel("Bandwidth Demand", fontsize=14)
        
        # Add grid and legend with improved styling
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.legend(loc='upper right', fontsize=10, framealpha=0.9)
        
        # Improve axis styling
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)
        
        # Adjust layout
        plt.tight_layout()
        
        # Save the figure if output path is provided
        if output_path:
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight')
            print(f"Scheduled demands visualization (no windows) saved to: {output_path}")
        
        return fig

    def visualize_dft_demands_only(self, job_ids=None, figsize=(16, 8), output_path=None, dpi=300):
        """
        Visualize only the DFT fitted communication demands for all jobs.
        This method shows the original communication patterns before window scheduling.
        
        Args:
            job_ids: List of job IDs to visualize. If None, all jobs are included.
            figsize: Figure size tuple (width, height)
            output_path: Output path for saving the visualization
            dpi: Resolution for saving the image
        
        Returns:
            matplotlib.figure.Figure: The created figure object
        """
        # Get all job IDs if not specified
        if job_ids is None:
            job_ids = list(self.scheduler.jobs.keys())
        
        # Create figure
        fig, ax = plt.subplots(figsize=figsize)
        
        # Generate improved colors for each job - more distinct and saturated
        colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', 
                  '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf']
        if len(job_ids) > len(colors):
            colors = plt.cm.Set1(np.linspace(0, 1, len(job_ids)))
        
        # Find the time range for all jobs
        max_original_time = 0
        min_original_time = float('inf')
        for job_id in job_ids:
            pattern = self.scheduler.jobs[job_id]
            min_original_time = min(min_original_time, pattern.start_time)
            max_original_time = max(max_original_time, pattern.end_time)
        
        # Create time points for evaluation
        dft_times = torch.linspace(min_original_time, max_original_time,
                                  self.time_resolution, device=self.device)
        
        # Calculate and plot individual job DFT demands
        total_dft_demand = torch.zeros_like(dft_times)
        
        for i, job_id in enumerate(job_ids):
            pattern = self.scheduler.jobs[job_id]
            
            # Calculate DFT demand for this job
            job_start = pattern.start_time
            job_end = pattern.end_time
            job_times = torch.linspace(job_start, job_end, self.time_resolution, device=self.device)
            job_dft_demand = pattern.evaluate_batch(job_times, True)  # True for DFT fitting
            
            # Interpolate to common time axis
            job_demand_interpolated = torch.zeros_like(dft_times)
            mask = (dft_times >= job_start) & (dft_times <= job_end)
            if mask.any():
                valid_times = dft_times[mask]
                # Linear interpolation
                indices = (valid_times - job_start) / (job_end - job_start) * (len(job_dft_demand) - 1)
                indices = indices.clamp(0, len(job_dft_demand) - 1)
                indices_floor = indices.floor().long()
                indices_ceil = indices.ceil().long().clamp(max=len(job_dft_demand) - 1)
                weights_ceil = indices - indices_floor
                weights_floor = 1.0 - weights_ceil
                
                interpolated_valid = job_dft_demand[indices_floor] * weights_floor + job_dft_demand[indices_ceil] * weights_ceil
                job_demand_interpolated[mask] = interpolated_valid
            
            # Plot individual job DFT demand curve with improved styling
            color = colors[i] if i < len(colors) else colors[i % len(colors)]
            ax.plot(dft_times.detach().cpu().numpy(), 
                   job_demand_interpolated.detach().cpu().numpy(),
                   label=f"{job_id} DFT", 
                   color=color, 
                   linewidth=2.5,
                   alpha=0.8)
            
            # Accumulate total demand
            total_dft_demand += job_demand_interpolated
        
        # Plot total DFT demand with emphasis
        ax.plot(dft_times.detach().cpu().numpy(), 
               total_dft_demand.detach().cpu().numpy(),
               label="Total DFT Demand", 
               color='black', 
               linewidth=4,
               linestyle='-',
               alpha=0.9)
        
        # Add bandwidth capacity line as reference
        ax.axhline(y=self.scheduler.bandwidth_capacity, 
                  color='red', 
                  linestyle='--',
                  linewidth=3,
                  alpha=0.8,
                  label=f"Bandwidth Capacity ({self.scheduler.bandwidth_capacity})")
        
        # Highlight over-capacity regions
        above_capacity = total_dft_demand > self.scheduler.bandwidth_capacity
        if above_capacity.any():
            ax.fill_between(dft_times.detach().cpu().numpy(), 
                           total_dft_demand.detach().cpu().numpy(),
                           self.scheduler.bandwidth_capacity, 
                           where=above_capacity.detach().cpu().numpy(),
                           color='red', 
                           alpha=0.2, 
                           label='Over Capacity')
        
        # Set labels and title
        ax.set_title("DFT Fitted Communication Demands - All Jobs (Before Scheduling)", fontsize=16, fontweight='bold')
        ax.set_xlabel("Time", fontsize=14)
        ax.set_ylabel("Bandwidth Demand", fontsize=14)
        
        # Add grid and legend with improved styling
        ax.grid(True, alpha=0.3, linestyle='-', linewidth=0.5)
        ax.legend(loc='upper right', fontsize=10, framealpha=0.9)
        
        # Improve axis styling
        ax.spines['top'].set_visible(False)
        ax.spines['right'].set_visible(False)
        ax.spines['left'].set_linewidth(1.2)
        ax.spines['bottom'].set_linewidth(1.2)
        
        # Adjust layout
        plt.tight_layout()
        
        # Save the figure if output path is provided
        if output_path:
            plt.savefig(output_path, dpi=dpi, bbox_inches='tight')
            print(f"DFT demands visualization saved to: {output_path}")
        
        return fig

# 4. DFT需求可视化（调度前）
visualizer.visualize_dft_demands_only(output_path="../history_experiment_results/dft_demands_only.png")
