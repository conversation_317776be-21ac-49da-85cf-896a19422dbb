import os
import time
from typing import Tuple, List, Dict, Any

import torch

from src.visualizer import EnhancedScheduleVisualizer
from src.window_parameters import WindowParameters


# 或者使用 log1p:
# from nonnegative_transforms import log1p_forward, log1p_inverse


class ScheduleOptimizer:
    """
    调度优化器类，负责优化窗口参数。

    使用基于梯度的优化算法寻找最优窗口参数，
    提供清晰的参数初始化、优化过程管理和结果获取接口。
    """


    def __init__(self,
                 objective_function,
                 learning_rate: float = 0.01,
                 max_iterations: int = 1000,
                 visualize: bool = True,
                 convergence_threshold: float = 1e-5):
        """
        初始化调度优化器

        Args:
            objective_function: 目标函数，接受WindowParameters并返回损失值
            learning_rate: 学习率
            max_iterations: 最大迭代次数
            convergence_threshold: 收敛阈值
        """
        self.objective_function = objective_function
        self.learning_rate = learning_rate
        self.max_iterations = max_iterations
        self.convergence_threshold = convergence_threshold
        self.visualize = visualize
        self.optimizer = None
        self.window_params = None
        self.visualizer = None #在训练的过程中是否可视化插空窗口

    def set_parameters(self, parameters: torch.Tensor) -> 'ScheduleOptimizer':
        """
        使用外部提供的参数覆盖当前窗口参数

        Args:
            parameters: 窗口参数的扁平化张量表示

        Returns:
            self: 返回优化器实例，支持链式调用
        """
        # 获取scheduler的window_params对象
        window_params = self.objective_function.scheduler.window_params

        if window_params is None:
            raise ValueError("调度器的窗口参数未初始化")

        # 设置参数
        window_params.set_flat_raw_params(parameters)

        # 重新初始化优化器
        return self.initialize_optimizer()


    def initialize_optimizer(self) -> 'ScheduleOptimizer':
        """
        初始化优化器，使用调度器中已有的窗口参数

        Returns:
            self: 返回优化器实例，支持链式调用
        """
        # 获取window_params
        window_params = self.objective_function.scheduler.window_params

        if window_params is None:
            raise ValueError("调度器的窗口参数未初始化，请先调用scheduler.initialize_windows")

        self.window_params = window_params

        # 确保参数可以被优化
        if not window_params.raw_params.requires_grad:
            window_params.raw_params.requires_grad_(True)

        # 创建优化器，只优化窗口的原始参数
        self.optimizer = torch.optim.SGD([window_params.raw_params], lr=self.learning_rate)
        # self.optimizer = torch.optim.Adam([window_params.raw_params], lr=self.learning_rate)
        return self

    def optimize(self) -> Tuple[WindowParameters, float, List[float]]:
        """
        执行优化过程，寻找最优窗口参数，并定期可视化窗口位置

        Returns:
            Tuple[WindowParameters, float, List[float]]:
                最优参数对象、最优目标函数值、优化历史记录
        """
        if self.optimizer is None or self.window_params is None:
            raise ValueError("必须先调用initialize_optimizer方法初始化优化器")

            # 创建可视化输出目录
        vis_dir = "optimization_progress"
        os.makedirs(vis_dir, exist_ok=True)

        history = []
        best_objective = float('inf')
        best_params = self.window_params.clone()

        # 记录初始目标函数值
        with torch.no_grad():
            initial_objective = self.objective_function(self.window_params)
            history.append(float(initial_objective))
            best_objective = float(initial_objective)
            print(f"初始目标函数值: {initial_objective:.6f}")

            # 可视化初始窗口位置
            if self.visualize:
                self._visualize_windows(
                    self.window_params,
                    f"{vis_dir}/initial_windows.png",
                    title=f"初始窗口位置 (目标函数值: {initial_objective:.6f})"
                )

            # 设置可视化频率
        visualization_interval = 50  # 每5次迭代可视化一次

        # 主优化循环
        iteration = 0  # 初始化iteration变量，确保循环外引用也有值
        for iteration in range(self.max_iterations):
            # 清除梯度
            self.optimizer.zero_grad()

            # 计算目标函数值
            objective = self.objective_function(self.window_params)

            # 反向传播
            objective.backward()

            # 更新参数（使用平滑变换，无需额外约束）
            self.optimizer.step()

            # 评估当前目标函数值
            with torch.no_grad():
                current_objective = float(self.objective_function(self.window_params))
                history.append(current_objective)

                # 更新最佳参数
                if current_objective < best_objective:
                    best_objective = current_objective
                    best_params = self.window_params.clone()

                    # 定期可视化
                if iteration % visualization_interval == 0 or iteration == self.max_iterations - 1:
                    print(f"迭代 {iteration}: 目标函数值 = {current_objective:.6f}")
                    if self.visualize:
                        self._visualize_windows(
                            self.window_params,
                            f"{vis_dir}/iteration_{iteration}_windows.png",
                            title=f"Iteration {iteration} window position (objective function value: {current_objective:.6f})"
                        )

                    # 检查收敛条件
                if iteration > 10 and abs(history[-1] - history[-2]) < self.convergence_threshold:
                    print(f"优化在第{iteration}次迭代收敛，目标函数值: {best_objective:.6f}")
                    if self.visualize:
                        # 使用最佳参数生成最终可视化
                        self._visualize_windows(
                            best_params,
                            f"{vis_dir}/final_best_windows.png",
                            title=f"Best window location (objective function value: {best_objective:.6f})"
                        )
                        break

                    # 检查是否达到最大迭代次数
        if iteration == self.max_iterations - 1:
            print(f"优化达到最大迭代次数 {self.max_iterations}，最终目标函数值: {best_objective:.6f}")

            # 生成最终可视化
        if self.visualize:
            self._visualize_windows(
                best_params,
                f"{vis_dir}/final_windows.png",
                title=f"Final window position (Objective function value: {best_objective:.6f})"
            )

        if self.visualize:
            # 生成优化历史曲线图
            self._plot_optimization_history(history, vis_dir)

        return best_params, best_objective, history





    def get_learning_curve(self, history: List[float]) -> Dict[str, Any]:
        """
        分析优化历史，返回学习曲线统计信息

        Args:
            history: 优化过程中的目标函数值历史

        Returns:
            Dict: 包含学习曲线分析的字典
        """
        improvement = history[0] - history[-1]
        relative_improvement = improvement / history[0] if history[0] != 0 else float('inf')

        return {
            "initial_value": history[0],
            "final_value": history[-1],
            "absolute_improvement": improvement,
            "relative_improvement": relative_improvement,
            "iterations": len(history) - 1,
            "converged": len(history) - 1 < self.max_iterations
        }


    def get_params(self) -> WindowParameters:
        """
        获取当前参数对象

        Returns:
            WindowParameters: 当前参数对象
        """
        if self.window_params is None:
            raise ValueError("优化器尚未初始化参数")
        return self.window_params


    def set_learning_rate(self, learning_rate: float) -> 'ScheduleOptimizer':
        """
        设置新的学习率

        Args:
            learning_rate: 新的学习率值

        Returns:
            self: 返回优化器实例，支持链式调用
        """
        self.learning_rate = learning_rate
        if self.optimizer is not None:
            for param_group in self.optimizer.param_groups:
                param_group['lr'] = learning_rate
        return self


    def reset(self) -> 'ScheduleOptimizer':
        """
        重置优化器状态，但保留参数

        Returns:
            self: 返回优化器实例，支持链式调用
        """
        if self.window_params is not None:
            self.initialize_optimizer()
        return self



    # 以下两个方法，用于可视化窗口位置和优化历史。后期整合到 visualizer.py文件
    def _visualize_windows(self, window_params, filename, title=None):
        """
        可视化窗口位置

        参数:
            window_params: 窗口参数对象
            filename: 输出文件路径
            title: 可选的图表标题
        """
        import matplotlib.pyplot as plt
        import matplotlib.patches as patches
        import numpy as np

        # 获取窗口参数
        if window_params is None:
            print("No window parameters are available for visualization.")
            return

            # 获取所有作业的窗口
        all_windows = window_params.get_transformed_params()
        num_jobs = len(all_windows)

        # 创建图形
        fig, ax = plt.subplots(figsize=(14, num_jobs * 1.5 + 1))

        # 计算时间范围
        min_time = float('inf')
        max_time = 0

        for job_windows in all_windows:
            for start, length in job_windows:
                start_val = start.item()
                length_val = length.item()
                min_time = min(min_time, start_val)
                max_time = max(max_time, start_val + length_val)

                # 添加一点边距
        time_range = max_time - min_time
        if time_range <= 0:
            time_range = 1.0
        min_time -= time_range * 0.05
        max_time += time_range * 0.05

        # 为每个作业创建颜色
        colors = plt.cm.tab10(np.linspace(0, 1, num_jobs))

        # 绘制每个作业的窗口
        for job_idx, job_windows in enumerate(all_windows):
            y_pos = num_jobs - job_idx

            # 绘制作业标识线
            ax.axhline(y=y_pos, color='gray', linestyle='-', alpha=0.3)

            # 在左侧添加作业标签
            ax.text(min_time, y_pos, f"Job {job_idx}", fontsize=10,
                    va='center', ha='right', bbox=dict(facecolor='white', alpha=0.7))

            # 绘制该作业的所有窗口
            for win_idx, (start, length) in enumerate(job_windows):
                start_val = start.item()
                length_val = length.item()

                # 创建矩形代表窗口
                rect = patches.Rectangle(
                    (start_val, y_pos - 0.4), length_val, 0.8,
                    linewidth=1, edgecolor=colors[job_idx],
                    facecolor=colors[job_idx], alpha=0.6
                )
                ax.add_patch(rect)

                # 窗口中心添加标签
                ax.text(start_val + length_val / 2, y_pos,
                        f"W{win_idx + 1}\n({length_val:.2f})",
                        fontsize=8, ha='center', va='center',
                        bbox=dict(facecolor='white', alpha=0.7))

                # 添加窗口边界线
                ax.axvline(x=start_val, color=colors[job_idx], linestyle='--', alpha=0.5)
                ax.axvline(x=start_val + length_val, color=colors[job_idx], linestyle='--', alpha=0.5)

                # 设置图表属性
        if title:
            ax.set_title(title, fontsize=14)
        ax.set_xlabel('Time', fontsize=12)
        ax.set_xlim(min_time, max_time)
        ax.set_ylim(0.5, num_jobs + 0.5)
        ax.set_yticks([])  # 隐藏y轴刻度

        # 添加网格线
        ax.grid(True, axis='x', alpha=0.3)

        # 保存图表
        plt.tight_layout()
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        plt.close(fig)
        print(f"The window position visualization has been saved to: {filename}")

    def _plot_optimization_history(self, history, output_dir):
        """
        绘制优化过程中目标函数值的变化曲线

        参数:
            history: 优化历史记录，包含每次迭代的目标函数值
            output_dir: 输出目录
        """
        import matplotlib.pyplot as plt
        import numpy as np

        plt.figure(figsize=(10, 6))

        # 绘制优化曲线
        plt.plot(range(len(history)), history, 'b-', linewidth=2)
        plt.scatter(range(len(history)), history, color='blue', s=30)

        # 标注最佳值
        best_iter = np.argmin(history)
        best_value = history[best_iter]
        plt.scatter([best_iter], [best_value], color='red', s=100, zorder=5)
        plt.annotate(f'Best: {best_value:.6f}',
                     xy=(best_iter, best_value),
                     xytext=(best_iter + 1, best_value * 1.1),
                     arrowprops=dict(facecolor='black', shrink=0.05, width=1.5),
                     fontsize=12)

        # 设置图表属性
        plt.title('Change in the value of the objective function during the optimization process', fontsize=14)
        plt.xlabel('Iteration times', fontsize=12)
        plt.ylabel('Objective function value', fontsize=12)
        plt.grid(True, alpha=0.3)

        # 添加起始点和终点标注
        plt.annotate(f'Start: {history[0]:.6f}',
                     xy=(0, history[0]),
                     xytext=(2, history[0] * 1.1),
                     arrowprops=dict(facecolor='green', shrink=0.05, width=1.5),
                     fontsize=12)

        plt.annotate(f'End: {history[-1]:.6f}',
                     xy=(len(history) - 1, history[-1]),
                     xytext=(len(history) - 3, history[-1] * 1.1),
                     arrowprops=dict(facecolor='purple', shrink=0.05, width=1.5),
                     fontsize=12)

        # 保存图表
        output_path = f"{output_dir}/optimization_history.png"
        plt.tight_layout()
        plt.savefig(output_path, dpi=300)
        plt.close()
        print(f"The optimized historical curve has been saved to: {output_path}")