import os
import json
import numpy as np
import torch
from concurrent.futures import Thr<PERSON><PERSON><PERSON>Executor, as_completed
from datetime import datetime

from src.contention_metrics import ContentionMetrics
from src.objective_function import ObjectiveFunction
from src.communication import CommunicationPattern
from src.data_generator import DataGenerator
from src.schedule_optimizer import ScheduleOptimizer
from src.window_parameters import WindowParameters
from src.window_scheduler import WindowScheduler

class DEOptimizer:
    """
    差分进化全局优化器（DE + 并行局部梯度优化），适用于窗口调度全局参数优化。
    """
    def __init__(
        self,
        num_jobs=2,
        seed=42,
        period_length=100.0,
        bandwidth_capacity=1.0,
        tau=0.1,
        num_windows=5,
        alpha=2.0,
        beta=1.0,
        gamma=0.2,
        mu=0.2,
        lambda1=1,
        lambda2=0.5,
        epsilon=1e-3,
        num_time_points=1000,
        learning_rate=0.75,
        convergence_threshold=1e-8,
        pop_size=50,
        max_generations=50,
        local_opt_steps=50,
        de_f=0.7,
        de_cr=0.7,
        n_threads=50,
        reinit_every=2,
        reinit_num=30,
        keep_top=20,
        results_dir="../results_de",
        device = "cpu"
            # device: str = "cuda" if torch.cuda.is_available() else "cpu"
    ):
        # 基本参数
        self.num_jobs = num_jobs
        self.seed = seed
        self.period_length = period_length
        self.bandwidth_capacity = bandwidth_capacity
        self.tau = tau
        self.num_windows = num_windows
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.mu = mu
        self.lambda1 = lambda1
        self.lambda2 = lambda2
        self.epsilon = epsilon
        self.num_time_points = num_time_points
        self.learning_rate = learning_rate
        self.convergence_threshold = convergence_threshold
        self.pop_size = pop_size
        self.max_generations = max_generations
        self.local_opt_steps = local_opt_steps
        self.de_f = de_f
        self.de_cr = de_cr
        self.n_threads = n_threads
        self.reinit_every = reinit_every
        self.reinit_num = reinit_num
        self.keep_top = keep_top
        self.results_dir = results_dir
        self.device = torch.device(device)
        os.makedirs(self.results_dir, exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.report_file = os.path.join(self.results_dir, f"de_report_{self.timestamp}.txt")
        self.result_file = os.path.join(self.results_dir, f"de_result_{self.timestamp}.json")
        np.random.seed(seed)
        torch.manual_seed(seed)
        # 初始化依赖对象
        self._init_dependencies()

    def _init_dependencies(self):
        # 数据集和通信模式
        data_generator = DataGenerator(random_seed=self.seed)
        dataset = data_generator.generate_test_dataset(num_jobs=self.num_jobs)
        self.communication_patterns = {}
        for job_id, raw_data in dataset.items():
            pattern = CommunicationPattern.from_raw_data(job_id=job_id, raw_data=raw_data)
            pattern = CommunicationPattern.extend_periods(pattern, num_periods=3, components_strategy='scaled')
            pattern.total_period_length = pattern.end_time - pattern.start_time
            self.communication_patterns[job_id] = pattern

        # 调度器
        self.scheduler = WindowScheduler(
            bandwidth_capacity=self.bandwidth_capacity,
            tau=self.tau
        )
        for job_id, pattern in self.communication_patterns.items():
            self.scheduler.add_job(job_id=job_id, pattern=pattern)
        self.schedule_init_func = lambda **kw: self.scheduler.initialize_windows(
            num_windows=self.num_windows,
            random_init=True,
            device=self.device,
            transform_type="softplus"
        )

        # 目标函数和优化器工厂
        self.contention_metrics = ContentionMetrics(
            alpha=self.alpha, beta=self.beta, gamma=self.gamma, mu=self.mu
        )
        self.objective_function = ObjectiveFunction(
            scheduler=self.scheduler,
            metrics=self.contention_metrics,
            lambda1=self.lambda1,
            lambda2=self.lambda2,
            epsilon=self.epsilon,
            num_time_points=self.num_time_points
        )
        self.optimizer_builder = self._build_optimizer()

    def _build_optimizer(self):
        """
        返回一个optimizer_builder: flat_param => (best_param, best_object, history)
        """
        def optimizer_fn(flat_param):
            # 重新设置窗口参数
            self.scheduler.set_window_params(flat_param)
            optimizer = ScheduleOptimizer(
                objective_function=self.objective_function,
                learning_rate=self.learning_rate,
                max_iterations=self.local_opt_steps,
                convergence_threshold=self.convergence_threshold
            )
            optimizer.initialize_optimizer(flat_param)
            best_param, best_obj, history = optimizer.optimize()
            return best_param.detach().cpu(), float(best_obj), history
        return optimizer_fn

    def _initialize_population(self):
        population = []
        for _ in range(self.pop_size):
            params = self.schedule_init_func(num_windows=self.num_windows, random_init=True, device=self.device)
            flat_param = params.get_flat_raw_params().detach().clone()
            population.append(flat_param)
        return population


    def _local_optimize_individual(self, flat_param):
        # 复制(或新建) scheduler/ObjectiveFunction/WindowParameters
        # 1. 新建 scheduler
        local_scheduler = WindowScheduler(
            bandwidth_capacity=self.bandwidth_capacity,
            tau=self.tau
        )
        for job_id, pattern in self.communication_patterns.items():
            local_scheduler.add_job(job_id=job_id, pattern=pattern)
        local_scheduler.initialize_windows(
            num_windows=self.num_windows,
            random_init=True,
            device=self.device,
            transform_type="softplus"
        )
        # 2. 用set_flat_raw_params设置参数
        # print(self.device)
        flat_param = flat_param.to(self.device)
        local_scheduler.window_params.set_flat_raw_params(flat_param)
        local_scheduler.window_params.raw_params = local_scheduler.window_params.raw_params.to(self.device)
        # print("当前raw_params设备：", local_scheduler.window_params.raw_params.device)

        # 3. 新建 objective_function
        local_objective_function = ObjectiveFunction(
            scheduler=local_scheduler,
            metrics=self.contention_metrics,
            lambda1=self.lambda1,
            lambda2=self.lambda2,
            epsilon=self.epsilon,
            num_time_points=self.num_time_points
        )
        # 4. 新建optimizer
        local_optimizer = ScheduleOptimizer(
            objective_function=local_objective_function,
            learning_rate=self.learning_rate,
            max_iterations=self.local_opt_steps,
            visualize=False,
            convergence_threshold=self.convergence_threshold
        )
        local_optimizer.initialize_optimizer()
        best_params, best_obj, _ = local_optimizer.optimize()
        best_flat_param = best_params.get_flat_raw_params().detach().cpu().clone()
        return best_flat_param, float(best_obj)


    def _parallel_local_optimize(self, population):
        results = [None] * len(population)
        with ThreadPoolExecutor(max_workers=self.n_threads) as executor:
            future_to_idx = {
                executor.submit(self._local_optimize_individual, flat_param): idx
                for idx, flat_param in enumerate(population)
            }
            for future in as_completed(future_to_idx):
                idx = future_to_idx[future]
                best_flat_param, best_obj = future.result()
                results[idx] = (best_flat_param, best_obj)
        return results


    def _de_mutation(self, population, idx, n_parents=4, sigma=0.02):
        candidates = list(range(len(population)))
        candidates.remove(idx)
        parent_ids = np.random.choice(candidates, n_parents, replace=False)
        # Step 1: flat raw -> flat physical
        parents_flat_physical = []
        for p in [population[i] for i in parent_ids]:
            wp = self.scheduler.window_params.clone()
            wp.set_flat_raw_params(p)
            parents_flat_physical.append(wp.get_flat_physical_params())
        # Step 2: 凸组合
        alpha = np.random.dirichlet(np.ones(n_parents))
        child_flat_physical = sum(a * p for a, p in zip(alpha, parents_flat_physical))
        # Step 3: 加扰动
        noise = torch.randn_like(child_flat_physical) * sigma * (child_flat_physical.abs() + 1e-6)
        child_flat_physical = torch.clamp(child_flat_physical + noise, min=1e-4)
        # Step 4: 转回raw参数
        wp = self.scheduler.window_params.clone()
        wp.set_flat_physical_params(child_flat_physical)
        child_raw_flat = wp.get_flat_raw_params().detach().clone()
        return child_raw_flat



    def _de_crossover(self, target, mutant):
        size = target.shape[0]
        trial = target.clone()
        cross_points = torch.rand(size) < self.de_cr
        if not cross_points.any():
            cross_points[np.random.randint(0, size)] = True
        trial[cross_points] = mutant[cross_points]
        return trial

    def _report_population_statistics(self, objs, generation):
        objs = np.array(objs)
        best = np.min(objs)
        top10 = np.percentile(objs, 10)
        mean = np.mean(objs)
        std = np.std(objs)
        msg = (
            f"Generation {generation:3d}: "
            f"Best: {best:.6f}, Top10%: {top10:.6f}, Mean: {mean:.6f}, Std: {std:.6f}"
        )
        print(msg)
        with open(self.report_file, "a") as f:
            f.write(msg + "\n")

    def run(self):
        """
        主优化流程
        """
        print(f"[DE] Initializing population of {self.pop_size}")
        population = self._initialize_population()
        print("[DE] Evaluating initial population (parallel local optimization)...")
        opt_results = self._parallel_local_optimize(population)
        population = [p for p, obj in opt_results]
        objs = [obj for p, obj in opt_results]

        # 记录全局最优
        best_idx = int(np.argmin(objs))
        global_best_param = population[best_idx].clone()
        global_best_obj = objs[best_idx]
        global_best_gen = 0

        self._report_population_statistics(objs, 0)

        for gen in range(1, self.max_generations + 1):
            new_population = []
            for idx in range(self.pop_size):
                mutant = self._de_mutation(population, idx)
                trial = self._de_crossover(population[idx], mutant)
                new_population.append(trial)

            print(f"[DE] Generation {gen}: local optimization (parallel)...")
            opt_results = self._parallel_local_optimize(new_population)
            new_population = [p for p, obj in opt_results]
            new_objs = [obj for p, obj in opt_results]

            # 选择操作
            for i in range(self.pop_size):
                if new_objs[i] < objs[i]:
                    population[i] = new_population[i]
                    objs[i] = new_objs[i]

            # 记录全局最优
            best_idx = int(np.argmin(objs))
            if objs[best_idx] < global_best_obj:
                global_best_param = population[best_idx].clone()
                global_best_obj = objs[best_idx]
                global_best_gen = gen

            self._report_population_statistics(objs, gen)

            # 多样性维护：每reinit_every代保留前keep_top个体，随机引入reinit_num新个体
            if gen % self.reinit_every == 0 and gen < self.max_generations:
                indices = np.argsort(objs)
                keep_population = [population[i].clone() for i in indices[:self.keep_top]]
                keep_objs = [objs[i] for i in indices[:self.keep_top]]
                # 随机生成新个体
                print(f"[DE] Generation {gen}: re-initializing {self.reinit_num} individuals")
                new_population = self._initialize_population()[:self.reinit_num]
                opt_results = self._parallel_local_optimize(new_population)
                new_population = [p for p, obj in opt_results]
                new_objs = [obj for p, obj in opt_results]
                # 合并
                population = keep_population + new_population
                objs = keep_objs + new_objs

        print(f"[DE] Optimization completed. Best objective: {global_best_obj:.6f} at generation {global_best_gen}")

        # 应用最优参数到调度器
        self.scheduler.set_window_params(global_best_param)
        # 保存结果
        result_data = {
            "parameters": {
                "num_jobs": self.num_jobs,
                "num_windows": self.num_windows,
                "bandwidth_capacity": self.bandwidth_capacity,
                "tau": self.tau,
                "alpha": self.alpha,
                "beta": self.beta,
                "gamma": self.gamma,
                "mu": self.mu,
                "lambda1": self.lambda1,
                "lambda2": self.lambda2,
                "epsilon": self.epsilon
            },
            "best_obj": global_best_obj,
            "best_gen": global_best_gen,
            "best_param": global_best_param.cpu().numpy().tolist(),
            "history_report": self.report_file,
            "timestamp": self.timestamp
        }
        with open(self.result_file, "w") as f:
            json.dump(result_data, f, indent=2)
        print(f"[DE] Result saved to {self.result_file}")
        return result_data

if __name__ == '__main__':
    # print(torch.cuda.is_available())
    optimizer = DEOptimizer()
    optimizer.run()