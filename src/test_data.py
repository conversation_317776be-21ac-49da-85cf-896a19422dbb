import json
import os

import matplotlib.pyplot as plt
import numpy as np

from src.data_generator import DataGenerator


def test_data_generator():
    """测试增强版DataGenerator类的功能"""
    print("===== 开始测试增强版数据生成器 =====")

    # 1. 使用默认参数初始化
    print("\n1. 使用默认参数初始化数据生成器...")
    generator = DataGenerator(random_seed=42)
    print("   默认参数设置完成")

    # 2. 使用默认参数生成小型数据集
    print("\n2. 使用默认参数生成测试数据集(3个作业)...")
    default_dataset = generator.generate_test_dataset(num_jobs=3)
    validate_dataset(default_dataset)

    # 3. 更新生成器参数
    print("\n3. 更新生成器参数...")
    generator.update_parameters(
        min_samples=60,  # 更多采样点
        max_samples=80,
        compute_phase_min_ratio=0.6,  # 更长的计算阶段
        compute_phase_max_ratio=0.8,
        peak_intensity_min=0.8,  # 更高的峰值强度
        peak_intensity_max=0.909
    )
    print("   参数更新完成")

    # 4. 使用更新后参数生成数据集
    print("\n4. 使用更新后参数生成数据集...")
    updated_dataset = generator.generate_test_dataset(num_jobs=3)
    validate_dataset(updated_dataset)

    # 5. 使用作业特定参数生成数据集
    print("\n5. 使用作业特定参数生成数据集...")
    job_params = [
        {"min_samples": 70, "max_samples": 80, "peak_intensity_min": 0.8},  # 任务0：更高峰值
        {"min_samples": 50, "max_samples": 60, "compute_phase_min_ratio": 0.3},  # 任务1：更短计算阶段
        {"min_samples": 90, "max_samples": 100, "rise_ratio_max": 0.5}  # 任务2：更长上升阶段
    ]
    custom_dataset = generator.generate_test_dataset(num_jobs=3, job_params=job_params)
    validate_dataset(custom_dataset)

    # 6. 可视化所有数据集
    print("\n6. 生成可视化图表...")
    visualize_dataset(default_dataset, "default")
    visualize_dataset(updated_dataset, "updated")
    visualize_dataset(custom_dataset, "custom")

    # 7. 生成完整数据集
    print("\n7. 生成完整实验数据集(10个作业)...")
    full_dataset = generator.generate_test_dataset(num_jobs=10)

    # 8. 保存数据
    save_path = "communication_dataset.json"
    save_dataset(full_dataset, save_path)
    print(f"   数据集已保存至: {save_path}")

    print("\n===== 测试完成 =====")


def validate_dataset(dataset):
    """验证生成的数据集是否符合预期"""
    print(f"  - 数据集包含 {len(dataset)} 个作业")

    for job_id, job_data in dataset.items():
        # 基本数据完整性检查
        assert 'demands' in job_data, f"{job_id} 缺少需求值数据"
        assert 'start_time' in job_data, f"{job_id} 缺少起始时间"
        assert 'end_time' in job_data, f"{job_id} 缺少结束时间"
        assert 'metadata' in job_data, f"{job_id} 缺少元数据"

        # 数据一致性检查
        demands = np.array(job_data['demands'])
        num_samples = len(demands)
        compute_length = job_data['metadata']['compute_phase_length']
        comm_length = job_data['metadata']['comm_phase_length']

        # 验证采样点数量
        assert compute_length + comm_length == num_samples, f"{job_id} 阶段长度和总长度不一致"

        # 验证计算阶段全为0
        assert np.all(demands[:compute_length] == 0), f"{job_id} 计算阶段存在非零需求"

        # 验证最后一个采样点需求为0
        assert demands[-1] == 0, f"{job_id} 最后一个采样点需求不为0"

        # 输出基本信息
        print(f"  - {job_id}: {num_samples}个采样点，计算阶段={compute_length}点({compute_length / num_samples:.1%})，" +
              f"通信阶段={comm_length}点({comm_length / num_samples:.1%})，最大需求={demands.max():.4f}")


def visualize_dataset(dataset, dataset_name):
    """可视化数据集的通信模式"""
    # 创建保存图表的目录
    os.makedirs(f"figures/{dataset_name}", exist_ok=True)

    # 为每个作业绘制通信需求曲线
    for job_id, job_data in dataset.items():
        demands = np.array(job_data['demands'])
        start_time = job_data['start_time']
        end_time = job_data['end_time']
        sample_interval = job_data['metadata']['sample_interval']
        time_points = np.linspace(start_time, end_time, len(demands))
        compute_length = job_data['metadata']['compute_phase_length']

        # 创建主图
        plt.figure(figsize=(10, 6))
        plt.plot(time_points, demands, 'b-', linewidth=2, label='Communication Demand')

        # 标记计算和通信阶段的分界
        if compute_length < len(time_points):
            comm_start_time = time_points[compute_length]
            plt.axvline(x=comm_start_time, color='r', linestyle='--', label='Communication Phase Start')

        # 标记结束点
        plt.plot(time_points[-1], demands[-1], 'ro', markersize=8, label='Task End')

        # 标记阶段
        if compute_length > 0:
            mid_compute = (start_time + time_points[compute_length - 1]) / 2 if compute_length > 1 else start_time
            plt.text(mid_compute, demands.max() * 0.8, 'Compute Phase',
                     horizontalalignment='center', color='green', fontsize=12)

        if compute_length < len(demands) - 1:
            mid_comm = (time_points[compute_length] + end_time) / 2
            plt.text(mid_comm, demands.max() * 0.8, 'Communication Phase',
                     horizontalalignment='center', color='red', fontsize=12)

        # 设置图表属性
        plt.title(f"{dataset_name} - {job_id} Communication Pattern")
        plt.xlabel("Time")
        plt.ylabel("Demand Value")
        plt.grid(True, linestyle='--', alpha=0.7)
        plt.legend()
        plt.tight_layout()

        # 保存图表
        plt.savefig(f"figures/{dataset_name}/{job_id}_demand_pattern.png")
        plt.close()

    # 创建所有作业的对比图
    plt.figure(figsize=(12, 8))
    for job_id, job_data in dataset.items():
        demands = np.array(job_data['demands'])

        # 对每个作业进行归一化时间处理，便于比较
        normalized_time = np.linspace(0, 1, len(demands))
        plt.plot(normalized_time, demands, label=job_id)

    plt.title(f"{dataset_name} - All Jobs Communication Pattern Comparison")
    plt.xlabel("Normalized Time")
    plt.ylabel("Demand Value")
    plt.grid(True)
    plt.legend()
    plt.tight_layout()
    plt.savefig(f"figures/{dataset_name}/all_jobs_comparison.png")
    plt.close()


def save_dataset(dataset, filename):
    """将数据集保存为JSON文件"""
    with open(filename, 'w') as f:
        json.dump(dataset, f, indent=2)


if __name__ == "__main__":
    # 运行测试代码
    test_data_generator()