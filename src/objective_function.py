from typing import Optional, Dict, Any

import torch


class ObjectiveFunction:
    """
    目标函数类，构建和计算优化目标函数，整合通信争用度量和正则化项。

    该类可以作为函数直接调用，计算当前窗口参数的目标函数值。
    它将调度决策问题转化为数学优化问题，提供目标函数计算和梯度评估。
    """

    def __init__(self,
                 scheduler,
                 metrics,
                 lambda1: float = 0.01,
                 lambda2: float = 0.005,
                 epsilon: float = 1e-2,
                 num_time_points: int = 1000):
        """
        初始化目标函数对象

        Args:
            scheduler: WindowScheduler对象，提供调度操作和通信需求计算
            metrics: ContentionMetrics对象，计算通信争用度量
            lambda1: 延迟正则化强度参数，控制对总延迟的惩罚
            lambda2: 间隔正则化强度参数，控制对窗口间隔的惩罚
            epsilon: 数值精度参数，用于窗口有效性判断和积分计算
            num_time_points: 积分评估点数量，控制数值积分的精度
        """
        self.scheduler = scheduler
        self.metrics = metrics
        self.lambda1 = lambda1
        self.lambda2 = lambda2
        self.epsilon = epsilon
        self.num_time_points = num_time_points

    def _get_device(self) -> torch.device:
        """
        获取计算设备，与调度器参数保持一致

        Returns:
            torch.device: 计算设备
        """
        # 从scheduler中获取参数的device
        params = self.scheduler.get_window_params()
        if isinstance(params, torch.Tensor):
            return params.device
        return torch.device("cpu")

    def _compute_max_end_time(self) -> float:
        """
        计算所有作业调度后的最晚结束时间，并断开梯度链

        Returns:
            float: 最晚结束时间
        """
        max_end_time = 0.0

        for job_id, pattern in self.scheduler.jobs.items():
            # 获取原始作业结束时间
            original_end_time = pattern.period_length #

            # 选择一个足够远的时间点，确保所有时隙窗口效应已完成
            far_time_point = original_end_time * 10  # 假设延迟不会大于原始时间的十倍

            # 计算该时间点的累积延迟，并断开梯度链
            final_delay = self.scheduler.compute_cumulative_delay(job_id, far_time_point).detach()

            # 更新全局最晚结束时间
            job_end_time = original_end_time + float(final_delay)
            max_end_time = max(max_end_time, job_end_time)

        # 添加安全边界（10%的额外时间）
        # max_end_time = max_end_time * 1.1 # 多个争用的任务的最大通信时间 * 1.1

        return max_end_time

    def __call__(self, window_params: Optional[torch.Tensor] = None) -> torch.Tensor:
        """
        计算当前窗口参数的目标函数值

        Args:
            window_params: 可选的窗口参数张量，如果提供则临时使用这些参数计算目标函数值

        Returns:
            torch.Tensor: 目标函数值（标量张量）
        """
        # 如果提供了参数，先保存当前参数，然后设置新参数
        # if window_params is not None:
        #     current_params = self.scheduler.get_window_params()
        #     self.scheduler.set_window_params(window_params)

        # 计算目标函数值
        objective = self.compute_objective()

        # 如果之前保存了参数，恢复原始参数
        # if window_params is not None:
        #     self.scheduler.set_window_params(current_params)

        return objective

    def compute_objective(self) -> torch.Tensor:
        """
        计算完整目标函数值，包括争用度量和正则化项

        Returns:
            torch.Tensor: 目标函数值（标量张量）
        """
        # # 计算通信争用积分（主目标）
        contention_term = self.compute_contention_integral()
        # print(f"通信争用积分: {contention_term.item():.6f}")

        # 计算延迟正则化项
        delay_term = self.delay_regularization_term()
        # print(f"延迟正则化项: {delay_term.item():.6f} (权重λ1={self.lambda1:.4f})")

        # 计算窗口间隔正则化项
        interval_term = self.interval_regularization_term()
        # print(f"窗口间隔正则化项: {interval_term.item():.6f} (权重λ2={self.lambda2:.4f})")

        # 组合所有项构成完整目标函数
        total_objective =   self.lambda1 * delay_term \
                            + self.lambda2 * interval_term \
                            + contention_term


        return total_objective

    def compute_contention_integral(self) -> torch.Tensor:
        """
        计算通信争用的积分项，作为主目标函数

        Returns:
            torch.Tensor: 争用积分值（标量张量）
        """
        # 计算积分的时间范围（开始时间和结束时间）
        start_time = 0.0
        end_time = self._compute_max_end_time()  # 使用断开梯度的最大结束时间

        # 创建时间点和积分权重
        device = self._get_device()
        time_points = torch.linspace(start_time, end_time, self.num_time_points, device=device)

        # 使用梯形法则计算积分权重
        dt = (end_time - start_time) / (self.num_time_points - 1)
        weights = torch.ones(self.num_time_points, device=device) * dt
        weights[0] = weights[-1] = dt / 2  # 边界点权重减半

        # 批量计算所有时间点的归一化带宽利用率
        loads = self.scheduler.batch_compute(
            self.scheduler.get_normalized_load,
            time_points
        )

        # 应用争用度量函数
        contention_values = self.metrics.pointwise_metric_batch(loads)

        # 使用梯形法则进行数值积分
        integral = torch.sum(contention_values * weights)

        # 添加梯度检查
        if integral.requires_grad:
            integral.register_hook(lambda grad: self._check_gradient(grad, "contention_integral"))

        return integral


    def _check_gradient(self, grad, name):
        """检查梯度的有效性"""
        if torch.isnan(grad).any():
            print(f"警告: {name}的梯度包含NaN")
        if torch.isinf(grad).any():
            print(f"警告: {name}的梯度包含Inf")

        grad_norm = torch.norm(grad).item()
        if grad_norm < 1e-10:
            print(f"警告: {name}的梯度接近于零 (范数={grad_norm:.10f})")
        elif grad_norm > 1e10:
            print(f"警告: {name}的梯度非常大 (范数={grad_norm:.2e})")

        return grad  # 返回原始梯度，不修改


    def delay_regularization_term(self) -> torch.Tensor:
        """
        计算延迟正则化项，对所有窗口的延迟批量应用二范数平方惩罚

        根据设计：J_{delay} = lambda1 * sum_i sum_j (delta_t_{i,j})^2

        Returns:
            torch.Tensor: 延迟正则化项值（标量张量）
        """
        # 直接获取原始参数张量
        window_params = self.scheduler.get_window_params()

        # 提取所有窗口的持续时间 (delta_t) - 这些是原始参数空间的值
        # window_params 的结构是 [t_1, delta_t_1, t_2, delta_t_2, ...]
        raw_delta_ts = window_params[1::2]  # 取所有偶数索引位置的值，即所有 delta_t

        # **修复：首先将原始参数转换为物理空间的正数**
        # 获取窗口参数对象，应用正向变换
        delta_ts = self.scheduler.window_params.transform_param(raw_delta_ts)

        # 惩罚项应该是对物理时间 delta_ts 的直接计算
        # 使用二范数平方惩罚，与注释和设计初衷一致
        total_delay_penalty = torch.sum(delta_ts ** 2)

        return total_delay_penalty


    def interval_regularization_term(self) -> torch.Tensor:
        """
        计算调度间隔正则化项的并行化实现，惩罚不合理的窗口安排

        根据设计：J_{interval} = lambda2 * sum_i sum_j (ω_i,j * ω_i,j+1 * exp(-(t_i,j+1 - (t_i,j + Δt_i,j))/τ))

        Returns:
            torch.Tensor: 间隔正则化项值（标量张量）
        """
        # 获取窗口参数字典
        window_params_dict = self.scheduler.get_window_params_dict()
        device = self._get_device()

        # 初始化总惩罚
        total_interval_penalty = torch.tensor(0.0, device=device)

        for job_id, windows in window_params_dict.items():
            # 只有当作业有多个窗口时才计算间隔惩罚
            if len(windows) <= 1:
                continue

            # 将窗口参数转换为张量形式
            windows_tensor = torch.tensor(windows, device=device)

            # 提取开始时间和持续时间
            t_values = windows_tensor[:, 0]  # 所有窗口的开始时间
            delta_t_values = windows_tensor[:, 1]  # 所有窗口的持续时间

            # print(t_values)

            # 计算窗口有效性权重
            omega_values = delta_t_values / (delta_t_values + self.epsilon)

            # 计算当前窗口结束时间
            end_times = t_values + delta_t_values

            # print(end_times)

            # 获取下一窗口开始时间（除最后一个窗口）
            next_start_times = t_values[1:]
            current_end_times = end_times[:-1]

            # 计算间隔
            intervals = next_start_times - current_end_times

            # 计算相邻窗口的有效性权重乘积
            omega_current = omega_values[:-1]
            omega_next = omega_values[1:]
            omega_products = omega_current * omega_next

            # 应用指数惩罚函数
            interval_penalties = omega_products * torch.exp(-intervals / self.scheduler.tau)

            # print(interval_penalties)

            # 累加所有间隔惩罚
            job_interval_penalty = torch.sum(interval_penalties)
            total_interval_penalty = total_interval_penalty + job_interval_penalty

        return total_interval_penalty



    def analyze_components(self, window_params: Optional[torch.Tensor] = None) -> Dict[str, Any]:
        """
        分析目标函数的各个组成部分

        Args:
            window_params: 可选的窗口参数张量

        Returns:
            Dict[str, Any]: 包含各组成部分值的字典
        """
        # 如果提供了参数，先保存当前参数，然后设置新参数
        if window_params is not None:
            current_params = self.scheduler.get_window_params()
            self.scheduler.set_window_params(window_params)

        # 计算各组成部分
        with torch.no_grad():
            contention_term = self.compute_contention_integral().item()
            delay_term = self.delay_regularization_term().item()
            interval_term = self.interval_regularization_term().item()

            # 计算总目标函数值
            total_objective = contention_term + self.lambda1 * delay_term + self.lambda2 * interval_term

        # 如果之前保存了参数，恢复原始参数
        if window_params is not None:
            self.scheduler.set_window_params(current_params)

        # 返回分析结果
        return {
            "total_objective": total_objective,
            "contention_term": contention_term,
            "delay_term": delay_term,
            "weighted_delay_term": self.lambda1 * delay_term,
            "interval_term": interval_term,
            "weighted_interval_term": self.lambda2 * interval_term,
            "integration_range": (0.0, self._compute_max_end_time()),
            "lambda1": self.lambda1,
            "lambda2": self.lambda2
        }