
# Introduction

分布式深度学习（Distributed Deep Learning，DDL）已成为推动人工智能发展的核心技术。然而，分布式训练中的性能瓶颈正逐渐从计算转向通信。特别是在多任务并行执行的场景中，通信争用成为影响训练效率的关键问题。当多个任务共享网络或链路时，显著增加了训练迭代时间，降低了GPU利用率。

为解决这一问题，研究人员提出了通信调度器来优化数据传输并减少通信争用。该调度器通过确定流量优先级和为多个网络流选择路径来解决争用问题。CASSINI作为一种作业间通信调度器的代表，通过预测每个作业的流量模式并应用相应的时间维度偏移来主动减少争用。然而，在多租户GPU集群中，每个作业的流量模式受到集群本身和其他并发运行作业的影响。因此，仅基于流量模式预测进行作业偏移无法消除通信争用。CASSINI的优化目标是通过交错的通信阶段最大化链路利用率；然而，这个过程本身可能会改变任务的通信周期，导致一个动态博弈。
**自干扰效应：**
例如，假设任务$j_1$和$j_2$通过时间偏移实现更高带宽，它们在全规约（AllReduce）阶段的通信时间可能从50ms减少到30ms。这可能导致任务迭代周期缩短（例如，从255ms缩短到235ms），从而改变统一圆周（最小公倍数周期）并使原始时间偏移失效。当任务周期变化时，需要重新计算最小公倍数并调整偏移；否则，兼容性得分将下降。

本文提出了一种解决深度学习工作负载间通信争用的新方法。我们提出的方法引入了**基于窗口函数的时间插入策略**，以平滑调整训练任务的通信调度。**通过构建连续且可微分的优化目标**，我们促进了高效的梯度优化。我们的贡献如下：

本工作的主要贡献如下：
- **分析性通信模式建模：**
我们提出使用更通用的分析方法对AI任务的通信模式建模，采用基于傅里叶变换（Fourier Transform）的频域采样来近似训练任务中的计算-通信模式。
- **基于可微窗口函数的时隙插入动作和调度行为建模：**
我们引入了使用可微窗口函数的时隙插入动作，并提出多个时隙插入动作的总体可微表示。以多个插入时隙动作后的通信需求曲线之和，对调度后的通信需求变化进行建模。
- **可微分争用度量优化目标：**
我们基于Lp范数，在总体通信需求的基础上，构建了连续可微的优化目标作为争用度量，以确保优化过程平滑调整训练作业的通信调度，促进了高效的基于梯度的优化。

# Motivation

## AI训练通信模式的建模与优化

分布式深度学习训练工作负载遵循结构化的计算-通信循环，其中每次迭代包括：
- **计算阶段：** 前向传播、反向传播和权重更新，主要利用GPU计算资源。
- **通信阶段：** 跨多个计算节点同步模型参数和梯度，这对带宽提出了重大需求。
在大规模AI集群中，多个作业并发运行，不同任务的通信阶段经常重叠，导致严重的**带宽争用**。优化作业调度的一个关键挑战是需要准确建模这些周期性通信模式，以实现有效的争用缓解。


## 现有方法的局限性
尽管作业调度策略已经有所进步，当前的方法仍然存在几个关键限制：
- **缺乏连续优化：** 大多数现有方法依赖于基于启发式的离散调度调整，无法充分利用作业通信模式的连续特性。
- **对动态工作负载缺乏灵活性：** 固定时间调度无法适应具有可变通信需求的动态变化的AI工作负载。
# 方法论

## 提出方法概述

我们提出了一个**可微分且动态的时隙窗口插入**框架，该框架通过对所有存在争用关系的AI作业总体通信需求曲线进行优化，以指导作业调度。我们的方法系统地建模了训练作业的周期性计算-通信循环，构建了一个基于调度动作平滑近似的可微分争用度量，并采用基于梯度的优化来确定最佳时隙窗口插入策略，从而减少带宽争用。提出的方法包括以下关键步骤：

1. **通信模式建模与近似：** 我们首先提取并解析地建模分布式训练作业的周期性通信模式。这是通过将离散的网络使用数据转换为平滑、可微分的函数表示来实现的，主要利用傅里叶变换将时域信号映射到频域，并保留主要频率成分以获得通信模式的精确近似。

2. **平滑时隙窗口插入动作设计：** 我们创新性地提出了时隙窗口插入动作的可微分表示。不同于传统的离散时间偏移，我们使用基于sigmoid函数的平滑窗口函数来建模插入操作，确保所有调度变换在数学上保持连续可微。这种设计允许调度决策可以通过梯度下降等方法进行精确优化。

3. **多时隙插入的可微分聚合：** 我们开发了一种方法，将多个时隙窗口插入动作的组合效果表示为一个连续可微分的函数。通过精心设计的抑制函数和累积延迟建模，我们实现了多个调度决策的平滑整合，使得整体通信模式在每个插入操作后仍保持可微分性。

4. **争用度量的可微分建模：** 我们引入了基于Lp范数的争用度量，用于量化修改后的通信需求曲线中的带宽竞争。这种度量取代了传统的最大值函数，提供了一个连续可微的优化目标，使得能够通过梯度下降高效地最小化峰值带宽使用。

5. **联合优化问题构建：** 我们将时隙窗口插入位置和持续时间的选择构建为一个联合优化问题。通过添加适当的正则化项以平衡争用减少和训练延迟，确保找到的解决方案既减少网络拥塞又不会过度延长作业完成时间。

6. **基于梯度的调度策略求解：** 利用构建的可微分优化框架，我们应用梯度下降等方法来高效求解最优时隙窗口插入策略。这种方法能够同时考虑多个作业的通信需求，找到全局最优的调度方案。



## 计算-通信循环提取与近似

准确地建模DNN训练中的计算-通信循环对优化时隙窗口调整和减少带宽争用至关重要。现有工作如CASSINI和Pollux依赖实际硬件profiling来获取不同配置下的通信特征，但这过程耗时耗资源。我们以此为基准，采用数学近似技术，将原始计算-通信信号转换为平滑、可微分的表示，从而高效模拟Traffic Pattern，具体来说如下：

### 问题定义与符号

设$f_j(t)$表示作业$j$随时间$t$的通信带宽利用函数。观察到的计算-通信需求可以分解为：

$$f_j(t) = C_j(t) + S_j(t)$$

其中$C_j(t)$表示**计算阶段**，而$S_j(t)$表示**通信阶段**。我们使用一个平滑函数$\tilde{f}_j(t)$近似$f_j(t)$，该函数保留其通信模式的周期性结构，同时允许连续优化。

### 计算-通信循环数据获取与处理

在构建通信需求的可微表示之前，首先需要获取和处理原始的通信数据。分布式深度学习作业表现出明显的迭代特性，因此我们采用以下步骤来提取和处理通信模式：

#### 通信模式采样

对于离散时间序列$\{(t_i, v_i)\}_{i=1}^n$，其中$t_i$表示时间点，$v_i$表示该时间点的网络带宽利用率。
#### 单任务通信需求预测

完整的通信需求曲线如下：

$$D_j(t) = \{v_i : t \equiv t_i \mod T_j\}$$

其中$T_j$表示作业$j$的迭代周期，$D_j(t)$表示时间$t$处的通信需求，通过网络带宽利用率表示。

### 通信需求函数的解析表达

#### 基于离散傅里叶变换的解析提取

为了构建优化框架所需的连续可微表示，我们需要将离散的通信需求采样数据转换为数学上严格的函数形式。傅里叶变换提供了一种理想的方法，能够捕捉通信需求的基本模式并保持数学上的可微性。通过应用离散傅里叶变换（Discrete Fourier Transform，DFT），我们可以将原始的通信需求数据$f_j(t)$表达为一系列频率成分的叠加：

$$\hat{f}_j(t) = \sum_{k=0}^{K} A_k \cos(2\pi k t / T_j) + B_k \sin(2\pi k t / T_j)$$

其中$\{A_k, B_k\}$是傅里叶系数，表示对应频率成分的幅度，$T_j$是任务生命周期的长度，$K$是选择的频率成分数量。这种表达方式不仅精确捕捉了通信需求的时域特征，更重要的是提供了一个处处可微的函数表示，为后续基于梯度的优化奠定了基础。通过控制参数$K$，我们可以平衡表示的精度和复杂度，确保在保留通信模式本质特征的同时，使得函数表示足够简洁，便于数值计算和优化处理。
#### 通信需求窗口化

为确保不同作业的通信需求在联合优化过程中能够正确呈现其时间特性，我们引入窗口函数来限制通信需求函数的有效作用域。这一步骤解决了傅里叶表示中的周期延拓问题，防止在作业实际生命周期之外产生"幽灵需求"。

给定作业集合$\{j_1, j_2, ..., j_M\}$及其对应的通信需求函数$\{g_{j_1}, g_{j_2}, ..., g_{j_M}\}$，我们为每个作业定义一个平滑窗口函数：

$$W_j(t) = \sigma\left(\frac{t - t_{start,j}}{\tau}\right) \cdot \left(1 - \sigma\left(\frac{t - (t_{end,j} + \Delta T_j)}{\tau}\right)\right)$$

其中$\sigma(x) = \frac{1}{1+e^{-x}}$为sigmoid函数，$t_{start,j}$和$t_{end,j}$分别是作业$j$的开始和结束时间（这个能否获得？），$\Delta T_j$是作业结束时刻的累积延迟，表示我们插入的所有窗口带来的累计延迟，$\tau$是控制窗口边缘平滑程度的参数。
窗口化后的通信需求函数表示为：

$$\tilde{f}_j(t) = W_j(t) \cdot \hat{f}_j(t)$$

此窗口化处理确保了每个作业的通信需求仅在其实际生命周期内有效，同时保持了函数的平滑性，为优化过程提供了准确的梯度信息。通过这种方式，不同作业的通信需求可以在任意长度的共同时间轴上进行加和和评估，无需考虑原始作业长度的差异。
#### DFT 解析优化

本节探讨了基于离散傅里叶变换（Discrete Fourier Transform, DFT）的通信模式表示方法，特别聚焦于频率成分数量对模型性质的根本影响，以及由此引发的方法学转变。
##### 通信模式表示
考虑单个任务通信需求序列 $\{f(t_i)\}_{i=0}^{N-1}$，其中 $f(t_i)$ 表示时间点 $t_i$ 处的通信需求，$N$ 为采样点总数。基于傅里叶理论，我们可以将该通信模式表示为三角函数的线性组合：

$$\hat{f}(t) = \frac{a_0}{2} + \sum_{k=1}^{K} \left[a_k \cos\left(\frac{2\pi k t}{T}\right) + b_k \sin\left(\frac{2\pi k t}{T}\right) \right]$$

其中，$T$ 为通信模式的周期（通常等于数据跨度 $t_{N-1} - t_0$），$a_k$ 和 $b_k$ 为傅里叶系数，$K$ 为所使用的频率成分数量。
##### 标准DFT与有限频率成分优化的理论比较
**标准DFT的完备性**
标准DFT理论指出，当使用全部 $N$ 个频率成分时（即 $K = \lfloor N/2 \rfloor$，考虑奈奎斯特采样定理），可以实现对原始离散数据的精确重建。在这种情况下，傅里叶系数可通过解析公式直接计算：

$$a_k = \frac{2}{N}\sum_{i=0}^{N-1}f(t_i)\cos\left(\frac{2\pi k t_i}{T}\right), \quad k = 0,1,\ldots,\lfloor N/2 \rfloor$$

$$b_k = \frac{2}{N}\sum_{i=0}^{N-1}f(t_i)\sin\left(\frac{2\pi k t_i}{T}\right), \quad k = 1,2,\ldots,\lfloor N/2 \rfloor$$

对于均匀采样且满足周期性边界条件的情况，重建函数将精确通过所有原始采样点，即 $\hat{f}(t_i) = f(t_i), \forall i \in \{0,1,\ldots,N-1\}$。

**约束频率成分的模型降维**
**核心命题**：当限制频率成分数量 $K < \lfloor N/2 \rfloor$ 时，傅里叶表示不再具备精确重建原始数据的能力，而转变为一个降维近似问题。
由于不再追求精确通过所有采样点，我们的目标转变为寻找最优的系数集合 $\{a_k, b_k\}_{k=0}^{K}$，使得重建函数与原始数据之间的误差最小化。这可以表述为以下最优化问题：

$$\min_{a_0, a_1, \ldots, a_K, b_1, \ldots, b_K} \sum_{i=0}^{N-1} \left| f(t_i) - \hat{f}(t_i) \right|^2$$
其中，$\hat{f}(t_i)$ 是使用有限频率成分重建的函数值。
这一最优化问题不再具有封闭形式的解析解，特别是当考虑到实际应用中的各种约束条件时（如非负性约束、窗口函数边界、非均匀采样等），需要采用数值优化方法求解。

**最优化求解方法**
针对约束频率成分下的傅里叶系数求解，我们提出了一种两阶段优化策略：
1. **全局探索阶段：** 使用Adam优化器进行初始拟合，具有自适应学习率调整能力，有助于克服可能的局部最优解问题。其更新规则为：
$$\theta_{t+1} = \theta_t - \eta \cdot \frac{m_t}{\sqrt{v_t} + \epsilon}$$
   其中 $\theta_t$ 表示第 $t$ 步的参数向量（包含所有 $a_k$ 和 $b_k$），$\eta$ 为学习率，$m_t$ 和 $v_t$ 分别为一阶和二阶矩估计。
2. **精细优化阶段：** 使用L-BFGS（Limited-memory Broyden-Fletcher-Goldfarb-Shanno）算法进行高精度局部优化。作为拟牛顿法的一种，L-BFGS通过近似计算Hessian矩阵的逆，提供了接近二阶收敛特性，同时保持了计算效率。
该两阶段策略结合了Adam的全局搜索能力和L-BFGS的局部精细优化特性，在保证收敛质量的同时提高了计算效率。

### 最终通信需求函数表示模型

通过选择合适的函数化表示方法，我们成功地构建了一个连续可微的通信需求函数：

$$\tilde{f}_j(t) = g_j(t, \theta)$$

其中$\theta$表示所选函数表示模型的参数集合。例如，在基于傅里叶变换的表示中，$\theta$对应于傅里叶系数$\{A_k, B_k\}$。这种精确的数学表示具有以下关键特性：

1. 处处连续可微，确保了在整个定义域上的梯度存在
2. 保留了原始通信需求模式的主要特征

此函数表示为后续的时隙窗口优化提供了坚实的数学基础，使我们能够应用高效的基于梯度的优化算法，而非受限于离散优化方法。无论是函数求值还是求导操作，都可以高效地在任意时间点上进行，这对于构建可微分的争用度量和实现动态时隙插入至关重要。

# 优化问题的数学建模

然而，为了在实际系统中有效应用这一机制，通过最优化过程得到通信调度具体的行为指导，需要系统地设计一个优化框架，确定时隙窗口的最优配置参数。本节旨在构建一个全局模型，以形式化表达多时隙窗口调度的优化问题。

我们首先分析全局通信需求的数学特性，然后逐步构建包含多种惩罚机制的目标函数，最后讨论约束条件和高效的求解策略。

## 通信需求的全局建模

给定$M$个并行执行的AI训练作业，每个作业$i \in \{1, 2, \dots, M\}$最多可被分配$N$个时隙窗口进行通信调度。设$t_{i,j}$和$\Delta t_{i,j}$分别表示作业$i$的第$j$个时隙窗口的起始时间和持续时间，其中$j \in \{1, 2, \dots, N\}$。基于前文定义的累积延迟函数和本节定义的时隙指示函数，作业$i$的调度后通信函数$g_i(t)$表示为：

$$g_i(t) = S_i(t) \cdot \tilde{f}_i\left(t - \Delta T_i(t)\right)$$

其中$S_i(t)$为作业$i$专属的时隙指示函数，$\Delta T_i(t)$为其累积延迟函数，定义如下：

$$S_i(t) = \prod_{j=1}^N \left[1 - \sigma\left(\frac{t - t_{i,j}}{\tau}\right) + \sigma\left(\frac{t - (t_{i,j} + \Delta t_{i,j})}{\tau}\right)\right]$$

$$\begin{aligned}
    \Delta T_i(t) &= \sum_{j=1}^N \Delta t_{i,j} \cdot \sigma\left(\frac{t - (t_{i,j} + \Delta t_{i,j})}{\tau}\right) \\
                &+ \sum_{j=1}^N (t - t_{i,j}) \cdot \left[\sigma\left(\frac{t - t_{i,j}}{\tau}\right) - \sigma\left(\frac{t - (t_{i,j} + \Delta t_{i,j})}{\tau}\right)\right] 
\end{aligned}$$

集群在任意时刻$t$的总通信需求$G(t)$可表示为所有作业通信需求的叠加：

$$G(t) = \sum_{i=1}^M g_i(t)$$

$G(t)$实质上表征了==整个集群在时间维度上的带宽资源需求分布==。当$G(t)$在某些时间点出现峰值时，意味着多个作业同时产生大量的网络通信需求，可能导致通信效率下降、作业完成时间增加。因此，通过优化每个作业的时隙窗口参数，我们可以调整$G(t)$的形态，使得在时间 $t$ 的通信需求降低，从而减轻网络资源争用，提高训练效率。

## 优化目标与约束条件

### 基本优化变量

在本文中，优化目标是为每个作业确定其各个时隙窗口的最佳配置。对于$M$个作业，每个作业最多分配$N$个时隙窗口，基本优化变量定义如下：

$$\theta = \{t_{i,j}, \Delta t_{i,j}\}_{i=1,j=1}^{M,N}$$

其中$t_{i,j}$表示作业$i$的第$j$个时隙窗口的起始时间，$\Delta t_{i,j}$表示其持续时间。优化问题的参数空间维度为$2MN$。理论上并非所有作业都需要使用全部$N$个窗口，==当$\Delta t_{i,j} < \epsilon$时($\epsilon$为最低调度动作阈值，当小于该阈值时，系统不会将该窗口视作调度动作)==，表示作业$i$的第$j$个窗口未被实际使用。这种参数化方式使得模型能够自适应地为每个作业分配所需的窗口数量，同时保持统一的优化框架。

值得注意的是，窗口参数的编号$j$虽然表示时间上的显式确认的顺序关系，但窗口$j$和窗口$j+1$在解空间中可以出现任意的时间顺序。但是该时间顺序隐式的内含于优化问题针对时间窗口参数的正则化项中。


在时间$t$，我们定义系统的归一化通信负载为$L(t) = G(t)/B$，其中$G(t)$为实际通信负载，$B$为带宽上限，则当且仅当$L(t) > 1$时出现通信争用。这种争用会导致通信延迟增加、吞吐量下降，甚至引发训练不稳定。采用归一化的带宽利用率$L(t)$使我们的模型不依赖于具体的带宽数值，提高了方法的通用性。

#### 通信调度的目标与挑战

基于对通信争用的理解，我们的核心目标是设计一种调度机制，使系统满足以下特性：

- 高效利用带宽资源，避免资源闲置（即维持较高的带宽利用率）
- 防止过度争用，确保通信需求不显著超过带宽上限（即$L(t)$不应显著超过1）
- 促进低负载时段的通信合并，提高整体效率

这一目标面临的主要挑战在于，我们需要一个数学上严格的、连续可微的度量函数，既能准确表达通信争用的代价，又能在优化过程中提供有效梯度。传统的分段函数虽直观但不连续可微，不适用于梯度优化算法。此外，理想的度量函数应对不同带宽利用率区间表现出不同的敏感度：当利用率低时鼓励增长，当接近但未超过上限时保持稳定，当超过上限时快速增长惩罚值。

#### 度量函数的设计

为克服上述挑战，我们设计了一个连续可微的通信度量函数（下称度量函数）：

$$F(L) = \alpha(L - (1-\mu))^2 + \beta \cdot (e^{\gamma(L-1)} - e^{-\gamma\mu})$$

其中，$L$表示归一化的带宽利用率$L = G(t)/B$，$\mu$为安全边界比例参数（$0 < \mu < 1$），$\alpha$、$\beta$和$\gamma$为正数权重参数。该函数由两部分组成：

- $\alpha(L - (1-\mu))^2$：抛物线项，在$L = 1-\mu$处取最小值
- $\beta \cdot (e^{\gamma(L-1)} - e^{-\gamma\mu})$：修正的指数项，在$L = 1-\mu$处取值为0，并随$L$增大而迅速增大

参数$\mu$表示我们期望将带宽利用率维持在距离上限$\mu$比例的安全距离处。例如，当$\mu = 0.05$时，系统将优化带宽利用率至95%处。

基于此度量函数，我们的时间积分目标函数为：

$$J = \int_{0}^{T} F(L(t)) \, dt$$

其中$L(t)$表示时间$t$处的归一化带宽利用率，$T = \text{detach}(\max_j(t_{end,j} + \Delta T_j))$为所有作业完成的最晚时间点。这里，我们使用$\text{detach}$操作阻断了$T$对优化变量的梯度传播。

我们将积分上限$T$定义为所有作业完成的最晚时间点，即$\max_j(t_{end,j} + \Delta T_j)$，其中$t_{end,j}$为作业$j$的原始结束时间，$\Delta T_j$为其调度延迟。特别地，我们在计算过程中使用了梯度阻断操作$\text{detach}$，这样做有两个重要考虑：

- **物理意义明确性**：积分上限$T$代表系统的总运行时间，具有明确的物理意义，不应作为直接的优化目标被任意调整。
- **避免优化冲突**：若不阻断梯度，延迟变量$\Delta T_j$将同时受到两个优化路径的影响——一方面通过正则项直接优化减少延迟，另一方面通过影响积分上限间接影响目标函数。这种"双重优化"可能导致梯度信号冲突和训练不稳定。
- **确保收敛稳定性**：阻断积分上限的梯度传播提供了更加稳定的优化环境，使优化过程更容易收敛到物理意义明确的解。

通过这种设计，我们确保了优化过程专注于调整作业的时间分布以最小化带宽竞争，同时通过单独的正则项控制总延迟，从而获得更加稳定且可解释的优化结果。

### 优化目标与正则化

#### 基本原则与设计思路

我们的核心目标是最小化系统中的通信争用，同时促进带宽资源的高效利用。为达成这一目标，我们构建了一个主目标明确、辅以适当正则化项的优化框架，而非传统的多目标优化方法。这种设计更符合问题的本质：通信争用抑制和复用促进是首要解决的问题，其他因素则作为约束条件或正则化项，确保调度方案在实际应用中的可行性和高效性。

#### 优化目标函数

我们的优化目标函数由主项和两个正则化项组成：

$$\begin{aligned}
    \mathcal{L}(\theta) = &\underbrace{\int_0^T F(L(t)) \, dt}_{\text{通信争用主目标}} + 
    \underbrace{\lambda_1 \sum_{i=1}^M e^{\Delta T_i}}_{\text{延迟正则化}} + \\
    &\underbrace{\lambda_2 \sum_{i=1}^M \sum_{j=1}^{N-1} \omega_{i,j} \omega_{i,j+1} \exp\left(-\frac{t_{i,j+1} - (t_{i,j} + \Delta_{i,j})}{\tau}\right)}_{\text{调度间隔正则化}}
\end{aligned}$$

其中$L(t) = G(t)/B$为归一化带宽利用率，$F(L)$为前文定义的度量函数，$\Delta T_i = [\Delta T_i(t_1), \Delta T_i(t_2), ..., \Delta T_i(t_n)]$表示作业$i$的各个调度窗口的长度组成的向量，$t_{i,j}$表示作业$i$的第$j$个调度窗口的开始时间，$\Delta_{i,j}$表示其持续时间，$\omega_{i,j}$为窗口有效性权重，$\tau$为时间尺度参数，$\lambda_1, \lambda_2 \geq 0$为正则化强度参数。各项的具体含义与设计理念将在后续段落详细阐述。

#### 通信争用主目标

该项直接采用前文设计的争用度量函数，对系统在整个时间区间内的争用状况进行积分：

$$J_{main} = \int_0^T F(L(t)) \, dt$$

其中$F(L) = \alpha(L - (1-\mu))^2 + \beta \cdot (e^{\gamma(L-1)} - e^{-\gamma\mu})$。该函数在$L = 1-\mu$处（即带宽利用率为理想值$(1-\mu)B$时）取得最小值0，小于该值时单调递减，大于该值时单调递增，且超过带宽上限后呈指数级增长。

这一设计确保了优化过程将：

- 积极促进带宽利用率向理想值$(1-\mu)B$靠拢
- 强烈惩罚超过带宽上限的争用行为
- 在低负载时段鼓励通信合并和资源共享

通过积分形式，我们考虑了整个时间区间内的累积争用情况，而非单纯关注瞬时峰值，这更符合分布式训练的长期优化需求。

#### 延迟正则化

为避免调度算法过度延迟某些作业，我们引入了基于二范数平方的延迟正则化项：

$$J_{delay} = \lambda_1  e^{\Delta T}  = \lambda_1 \sum_{j=1}^M  e^{\Delta T_j}  = \lambda_1 \sum_{j=1}^M \sum_{i=1}^n e^{\Delta T_j(t_i)}$$

这一设计同时考虑了总体延迟和极端延迟两个方面：

- 通过$e^x$的形式，对较大的延迟值施加更强的惩罚，隐式地限制了最大延迟
- 通过求和操作，考虑了系统引入的总体延迟

参数$\lambda_1$控制系统对延迟的总体敏感度，较大的$\lambda_1$值会使系统更倾向于及时处理通信需求，即使可能导致更多的通信争用。

#### 调度间隔正则化

基于前文对通信调度问题的分析，我们需要确保同一作业的多个调度窗口在时间上分布合理，避免过于接近但未合并的低效调度。考虑到每个调度窗口由开始时间和持续时间确定，并且窗口存在明确的顺序关系，我们设计了以下调度间隔正则化项：

$$J_{interval} = \lambda_2 \sum_{i=1}^M \sum_{j=1}^{N-1} \omega_{i,j} \omega_{i,j+1} \exp\left(-\frac{t_{i,j+1} - (t_{i,j} + \Delta_{i,j})}{\tau}\right)$$

其中：

- $M$表示系统中的作业总数，$N$表示每个作业的预设调度窗口数量
- $t_{i,j}$表示作业$i$的第$j$个调度窗口的开始时间
- $\Delta_{i,j}$表示作业$i$的第$j$个调度窗口的持续时间
- $\omega_{i,j} = \frac{\Delta_{i,j}}{\Delta_{i,j} + \epsilon} = \frac{1}{1 + \frac{\epsilon}{\Delta_{i,j}}}$是窗口有效性权重
- $\tau$为时间尺度参数，控制惩罚的作用范围

我们选择的窗口有效性权重函数$\omega_{i,j}$是一个连续可微的sigmoid类函数，具有以下特性：

- 当$\Delta_{i,j} \gg \epsilon$时，$\omega_{i,j} \approx 1$，表示窗口完全有效
- 当$\Delta_{i,j} \ll \epsilon$时，$\omega_{i,j} \approx \frac{\Delta_{i,j}}{\epsilon} \approx 0$，表示窗口接近无效
- 在整个定义域上保持连续可微，确保优化过程的稳定性
- 当$\Delta_{i,j} = \epsilon$时，$\omega_{i,j} = 0.5$，提供了平滑的过渡
该正则化项巧妙地结合了多个关键考量：
- **相邻窗口关系**：只对时间上连续的窗口对施加约束，符合调度的顺序性质
- **隐式窗口顺序促进**：当后一个窗口的开始时间早于前一个窗口的开始时间时，快速增长的指数项会带来巨大的惩罚，从而规避该情况
- **间隔直接度量**：通过计算后一窗口开始时间与前一窗口结束时间之差，直接表征实际间隔
- **窗口有效性处理**：权重函数$\omega_{i,j}$确保那些在优化过程中持续时间趋近于零的窗口不会产生不必要的惩罚影响
- **连续可微性保证**：整个表达式在参数空间中保持连续可微，与前文设计的争用度量函数在数学特性上保持一致
参数$\epsilon$代表系统中有意义的最小时间单位，当窗口持续时间$\Delta_{i,j}$小于$\epsilon$时，该窗口在正则化计算中的影响将按比例降低。参数$\tau$控制系统对间隔大小的敏感度，较小的$\tau$值使惩罚效应更加局部化，仅对极为接近的窗口产生显著影响；较大的$\tau$值则扩展惩罚的作用范围，鼓励调度窗口之间保持更大的间隔。

当两个调度窗口之间的间隔接近于零或出现重叠（$t_{j,i+1} \leq t_{j,i} + \Delta_{j,i}$）时，指数项将产生较大值，从而施加强烈惩罚。这种机制有效防止了调度方案中出现"本可合并但未合并"的低效情况，同时不会对间隔足够大的窗口对产生显著影响。
### 约束条件

为确保调度方案的实用性和有效性，我们对优化变量施加以下约束条件：

$$\begin{aligned}
&t_{i,j} \geq 0, \quad \forall i \in \{1,2,\ldots,M\}, j \in \{1,2,\ldots,N\} \quad \text{(非负时间)} \\
&\Delta t_{i,j} \geq 0, \quad \forall i \in \{1,2,\ldots,M\}, j \in \{1,2,\ldots,N\} \quad \text{(非负持续时间)} \\
\end{aligned}$$

这两个约束确保插入的时隙窗口在时间上具有物理意义。
除上述显式约束外，我们的优化框架还隐式地处理了以下实际需求：

- **调度顺序约束**：通过调度间隔正则化项，我们巧妙地避免了显式定义窗口的时间顺序，而是让优化过程自然形成合理的顺序安排。

- **窗口数量自适应**：通过窗口有效性权重函数$\omega_{i,j}$，系统能够自动将不必要的窗口持续时间优化至接近零，实现窗口数量的自适应优化，而无需显式约束每个作业必须使用固定数量的窗口。

- **带宽容量约束**：虽然没有直接限制瞬时带宽利用率$L(t)$，但通过主目标中对高利用率的指数级惩罚，系统被强力引导避免严重的带宽过载情况。

这种约束设计既保持了问题的数学处理性，又确保了解决方案的实际可行性，为后续优化算法的应用奠定了基础。在实际系统部署中，这些约束参数可根据具体应用场景和系统特性进行适当调整。

### 显式约束的处理方法

本优化问题包含两个基本的显式约束：

$$\begin{aligned}
&t_{i,j} \geq 0, \quad \forall i \in \{1,2,\ldots,M\}, j \in \{1,2,\ldots,N\} \quad \text{(非负时间)} \\
&\Delta t_{i,j} \geq 0, \quad \forall i \in \{1,2,\ldots,M\}, j \in \{1,2,\ldots,N\} \quad \text{(非负持续时间)} \\
\end{aligned}$$

这些是基本的非负约束，我们采用 softplus，将无约束参数映射到非负域。











