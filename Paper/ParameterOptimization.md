# Paramater Optimization
参数$\mu$代表安全边界比例，决定了系统倾向于将带宽利用在距离上限多远的位置。较小的$\mu$值（如0.01）使系统追求更接近带宽上限的使用率（99%），而较大的$\mu$值（如0.1）则提供更多安全余量（90%利用率）。在实际部署中，$\mu$的选择应考虑系统的稳定性和通信负载的波动特性。

参数$\alpha$、$\beta$和$\gamma$控制各区间的惩罚强度：

- $\alpha$控制抛物线项的陡峭程度，影响系统对偏离理想利用率的敏感度
- $\beta$控制指数惩罚项的整体强度，决定争用惩罚的幅度
- $\gamma$控制指数增长的速率，决定超出带宽上限后惩罚增长的陡峭程度

这些参数的合理配置能够在保持函数数学特性的同时，适应不同的网络环境和应用场景需求。在我们的实验中，典型的参数配置为$\mu = 0.05$，$\alpha = 1.0$，$\beta = 0.2$，$\gamma = 10.0$，此配置使系统在95%带宽利用率处达到最优，并对超过带宽上限的行为施加快速增长的惩罚。

通过这一设计，我们的争用度量函数在数学上是严格的、连续可微的，同时在应用层面准确捕捉了通信争用的代价，为后续的调度优化奠定了坚实基础。整个度量框架不依赖于特定的带宽数值，可以无缝适应不同规模和拓扑的分布式系统。