import torch

from src.nonnegative_transforms import softplus_forward, softplus_inverse, log1p_forward, log1p_inverse


class WindowParameters:
    """
    窗口参数管理类，负责存储和转换窗口调度的参数。

    使用平滑变换确保参数满足非负约束，并为优化过程提供参数访问接口。
    每个窗口由起始时间和持续时间两个参数定义。
    """


    def __init__(self, num_jobs: int, num_windows: int, device: str = "cpu",
                 transform_type: str = "softplus"):
        """
        初始化窗口参数

        Args:
            num_jobs: 作业数量
            num_windows: 每个作业的窗口数量
            device: 计算设备
            transform_type: 变换类型，支持"softplus"和"log1p"
        """
        # 内部存储原始无约束参数
        # 第一维：作业，第二维：窗口，第三维：[起始时间raw参数, 持续时间raw参数]
        self.raw_params = torch.zeros(num_jobs, num_windows, 2, device=device)
        self.num_jobs = num_jobs
        self.num_windows = num_windows
        self.device = device

        # 设置使用的变换函数
        if transform_type == "softplus":
            self.forward_fn = softplus_forward
            self.inverse_fn = softplus_inverse
        elif transform_type == "log1p":
            self.forward_fn = log1p_forward
            self.inverse_fn = log1p_inverse
        else:
            raise ValueError(f"不支持的变换类型: {transform_type}")


    def initialize_dirichlet_single_job(self, job_idx: int, pattern, tau: float = 0.1):
        """
        为单个作业使用Dirichlet缝隙分段法初始化窗口参数

        Args:
            job_idx: 作业索引
            pattern: 作业的通信模式对象
            tau: 时间平滑参数，用于确定最小持续时间

        Returns:
            self: 返回参数对象，支持链式调用
        """
        if job_idx >= self.num_jobs:
            raise ValueError(f"作业索引({job_idx})超出范围({self.num_jobs})")

        # 获取原始通信曲线长度
        original_length = pattern.total_period_length

        # 步骤1：用Dirichlet方法获得零持续时间假设下的初始时间
        initial_start_times = self._dirichlet_gap_positions(
            self.num_windows, original_length
        )

        # 步骤2：采样窗口持续时间
        durations = self._sample_window_durations(
            self.num_windows, original_length, tau
        )

        # 步骤3：根据窗口持续时间更新起始时间
        final_start_times = self._update_start_times_with_durations(
            initial_start_times, durations
        )

        # 反向变换为原始参数，只更新指定作业的参数
        self.raw_params[job_idx, :, 0] = self.inverse_fn(final_start_times)
        self.raw_params[job_idx, :, 1] = self.inverse_fn(durations)

        return self


    def _dirichlet_gap_positions(self, num_windows: int, original_length: float):
        """
        Dirichlet分段法：获得k+1个分段点，假设窗口持续时间为0
        返回k个窗口在原始长度上的起始位置
        """
        # 从Dirichlet(1,1,...,1)采样k+1个权重
        alpha = torch.ones(num_windows + 1, device=self.device)
        weights = torch.distributions.Dirichlet(alpha).sample()  # sum = 1

        # 转换为累积位置
        cumulative_positions = torch.cumsum(weights * original_length, dim=0)

        # 返回前k个位置作为窗口起始时间（第k+1个位置是结束位置）
        start_times = cumulative_positions[:-1]  # 取前k个

        return start_times


    def _sample_window_durations(self, num_windows: int, original_length: float, tau: float):
        """采样窗口持续时间"""
        # 参数设置
        duration_min = max(3 * tau, original_length * 0.01)  # 基于数值稳定性
        total_duration_budget = original_length * 0.5  # 总预算为原始长度的50%
        duration_max = total_duration_budget / num_windows

        # 确保duration_max > duration_min
        duration_max = max(duration_max, duration_min * 2)

        # 对数空间采样
        log_min = torch.log(torch.tensor(duration_min, device=self.device))
        log_max = torch.log(torch.tensor(duration_max, device=self.device))
        log_durations = log_min + torch.rand(num_windows, device=self.device) * (log_max - log_min)
        durations = torch.exp(log_durations)

        return durations


    def _update_start_times_with_durations(self, initial_start_times: torch.Tensor,
                                           durations: torch.Tensor):
        """
        根据窗口持续时间更新起始时间
        每个窗口的插入会让后续所有窗口向右移动
        """
        # 计算每个窗口需要累积的偏移量
        # 第i个窗口需要加上前面所有窗口的持续时间
        cumulative_durations = torch.zeros_like(durations)
        cumulative_durations[1:] = torch.cumsum(durations[:-1], dim=0)

        # 一次性更新所有起始时间
        updated_start_times = initial_start_times + cumulative_durations

        return updated_start_times



































    # TODO 窗口初始化有点问题
    def initialize_random(self,
                          min_start_time: float = 0.0,
                          max_start_time: float = 100.0,
                          min_duration: float = 1.0,
                          max_duration: float = 20.0,
                          enforce_order: bool = True,
                          max_overlap_ratio: float = 0.0):

        """
        随机初始化窗口参数

        Args:
            min_start_time: 起始时间的最小值
            max_start_time: 起始时间的最大值
            min_duration: 持续时间的最小值
            max_duration: 持续时间的最大值
            enforce_order: 是否强制窗口按时间顺序排列
            max_overlap_ratio: 允许的最大重叠比例（0.0表示无重叠）

        Returns:
            self: 返回参数对象，支持链式调用
        """
        # 随机生成窗口参数
        if enforce_order:
            # 按顺序生成窗口，避免重叠
            # 对每个作业分别处理
            start_times = torch.zeros(self.num_jobs, self.num_windows, device=self.device)
            durations = torch.zeros(self.num_jobs, self.num_windows, device=self.device)

            time_range = max_start_time - min_start_time

            # print(time_range)

            for job_idx in range(self.num_jobs):
                # 为每个作业划分若干个不重叠的时间段
                # 计算每个窗口可分配的平均时间段长度
                avg_segment_length = time_range / self.num_windows

                # print(avg_segment_length)

                # 为每个窗口设置一个位置（均匀分布但有随机性）
                # 将时间范围划分为num_windows段，每个窗口落在对应的段内
                for window_idx in range(self.num_windows):
                    # 确定该窗口的时间区间范围
                    segment_start = min_start_time + window_idx * avg_segment_length
                    segment_end = segment_start + avg_segment_length

                    # 在区间内随机选择一个起始时间（不靠近边界）
                    buffer = avg_segment_length * 0.1  # 与边界保持一定距离
                    window_start = segment_start + buffer + torch.rand(1, device=self.device)[0] * (
                                avg_segment_length - 2 * buffer)

                    # 生成持续时间（确保不会过长导致重叠到下一窗口）
                    max_window_duration = min(
                        max_duration,
                        avg_segment_length * (0.8 + max_overlap_ratio)  # 允许一定比例的重叠
                    )

                    # print(max_window_duration)

                    window_duration = min_duration + torch.rand(1, device=self.device)[0] * (
                                max_window_duration - min_duration)

                    # 设置参数
                    start_times[job_idx, window_idx] = window_start
                    durations[job_idx, window_idx] = window_duration
        else:
            # 完全随机生成（可能导致重叠或逆序）
            start_times = min_start_time + torch.rand(self.num_jobs, self.num_windows, device=self.device) * (
                    max_start_time - min_start_time)
            durations = min_duration + torch.rand(self.num_jobs, self.num_windows, device=self.device) * (
                    max_duration - min_duration)

        # 反向变换为原始参数
        # print(start_times)
        # print(durations)

        self.raw_params[:, :, 0] = self.inverse_fn(start_times)
        self.raw_params[:, :, 1] = self.inverse_fn(durations)


        # print(start_times)
        # print(durations)

        # 确保参数可被优化
        self.raw_params.requires_grad_(True)
        return self


    def initialize_from_features(self, job_features, enforce_order: bool = True, overlap_buffer: float = 0.1):
        """
        根据作业特征初始化窗口参数

        Args:
            job_features: 列表，每个元素为(start_time, period_length)元组，
                         代表作业的开始时间和周期长度
            enforce_order: 是否强制窗口按时间顺序排列
            overlap_buffer: 窗口间的缓冲区比例，用于防止重叠

        Returns:
            self: 返回参数对象，支持链式调用
        """
        if len(job_features) != self.num_jobs:
            raise ValueError(f"作业特征数量({len(job_features)})与作业数量({self.num_jobs})不匹配")

        # 创建存储实际参数值的临时张量
        start_times = torch.zeros(self.num_jobs, self.num_windows, device=self.device)
        durations = torch.zeros(self.num_jobs, self.num_windows, device=self.device)

        # 根据作业特征设置窗口参数
        for i, (job_start, job_period) in enumerate(job_features):
            # 有效时间范围 = 周期长度的前半部分
            effective_range = job_period * 0.5

            if enforce_order:
                # 计算每个窗口可分配的时间段长度
                segment_length = effective_range / self.num_windows

                # 计算可用于窗口的最大持续时间（避免过度重叠）
                max_window_duration = segment_length * (1.0 - overlap_buffer)

                # 窗口按顺序排列
                for j in range(self.num_windows):
                    # 窗口起始时间 = 作业开始时间 + 该窗口的时间段起点 + 一些随机偏移
                    segment_start = job_start + j * segment_length
                    random_offset = torch.rand(1, device=self.device)[0] * segment_length * overlap_buffer

                    start_times[i, j] = segment_start + random_offset

                    # 窗口持续时间，确保不会太长导致重叠到下一窗口
                    window_duration = job_period * 0.05  # 初始设置为周期的5%

                    # 如果窗口太长，可能超过下一个窗口的开始，则适当缩短
                    if window_duration > max_window_duration:
                        window_duration = max_window_duration * (0.5 + torch.rand(1, device=self.device)[0] * 0.5)

                    durations[i, j] = window_duration
            else:
                # 原有逻辑：窗口均匀分布，但可能会有重叠
                for j in range(self.num_windows):
                    # 根据窗口索引计算相对位置（均匀分布）
                    relative_pos = (j + 1) / (self.num_windows + 1)

                    # 计算窗口起始时间（基于作业开始时间和相对位置）
                    start_times[i, j] = job_start + relative_pos * effective_range

                    # 设置初始窗口持续时间（基于周期长度的一小部分）
                    durations[i, j] = job_period * 0.05

        # 反向变换为原始参数


        self.raw_params[:, :, 0] = self.inverse_fn(start_times)
        self.raw_params[:, :, 1] = self.inverse_fn(durations)


        # 确保参数可被优化
        self.raw_params.requires_grad_(True)
        return self

    def transform_param(self, raw_param: torch.Tensor) -> torch.Tensor:
        """
        将原始参数变换为非负实际值

        Args:
            raw_param: 原始参数张量

        Returns:
            torch.Tensor: 变换后的非负参数张量
        """
        return self.forward_fn(raw_param)


    def get_transformed_params(self) -> torch.Tensor:
        """
        获取变换后的实际窗口参数

        Returns:
            torch.Tensor: 形状为[num_jobs, num_windows, 2]的张量，
                         每个元素为[起始时间, 持续时间]
        """
        start_times = self.transform_param(self.raw_params[:, :, 0])
        durations = self.transform_param(self.raw_params[:, :, 1])

        # 生成形状为[num_jobs, num_windows, 2]的张量
        transformed = torch.stack([start_times, durations], dim=2)
        return transformed


    # def get_flat_raw_params(self) -> torch.Tensor:
    #     """
    #     获取展平的原始参数，用于优化器
    #
    #     Returns:
    #         torch.Tensor: 展平的原始参数张量
    #     """
    #     return self.raw_params.view(-1)
    #
    #
    # def set_flat_raw_params(self, flat_params: torch.Tensor):
    #     """
    #     从展平的原始参数更新内部参数表示
    #
    #     Args:
    #         flat_params: 展平的原始参数张量
    #     """
    #     self.raw_params = flat_params.view(self.num_jobs, self.num_windows, 2)
    #     self.raw_params = self.raw_params.to(self.device)


    def clone(self) -> 'WindowParameters':
        """
        创建参数的深拷贝

        Returns:
            WindowParameters: 参数对象的深拷贝
        """
        result = WindowParameters(self.num_jobs, self.num_windows, self.device)
        result.raw_params = self.raw_params.clone().detach()
        # 复制变换函数
        result.forward_fn = self.forward_fn
        result.inverse_fn = self.inverse_fn
        return result


    def get_window_end_times(self) -> torch.Tensor:
        """
        计算所有窗口的结束时间（起始时间+持续时间）

        Returns:
            torch.Tensor: 窗口结束时间张量
        """
        transformed = self.get_transformed_params()
        start_times = transformed[:, :, 0]
        durations = transformed[:, :, 1]
        return start_times + durations



    def get_flat_raw_params(self) -> torch.Tensor:
        """获取展平的原始参数（1D向量，适合优化器/DE种群存储）"""
        return self.raw_params.view(-1)

    def set_flat_raw_params(self, flat_params: torch.Tensor):
        """从展平的原始参数更新内部参数"""
        self.raw_params = flat_params.view(self.num_jobs, self.num_windows, 2).to(self.device)

    def get_flat_physical_params(self) -> torch.Tensor:
        """获取展平的物理空间参数（1D向量）"""
        physical = self.get_physical_params()
        return physical.view(-1)

    def set_flat_physical_params(self, flat_physical: torch.Tensor):
        """从展平的物理参数还原raw参数，并更新内部参数"""
        shape = (self.num_jobs, self.num_windows, 2)
        physical = flat_physical.view(*shape)
        self.raw_params = self.inverse_fn(physical).to(self.device)

    def get_physical_params(self) -> torch.Tensor:
        """获取物理空间参数（二维/三维张量）"""
        return self.forward_fn(self.raw_params)

    def set_physical_params(self, physical_params: torch.Tensor):
        """直接用物理空间参数（二维/三维张量）设置raw参数"""
        self.raw_params = self.inverse_fn(physical_params).to(self.device)