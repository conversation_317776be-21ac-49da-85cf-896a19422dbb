import os
import json
import numpy as np
import torch
from datetime import datetime
import cma  # pip install cma

from src.contention_metrics import ContentionMetrics
from src.objective_function import ObjectiveFunction
from src.communication import CommunicationPattern
from src.data_generator import DataGenerator
from src.schedule_optimizer import ScheduleOptimizer
from src.window_parameters import WindowParameters
from src.window_scheduler import WindowScheduler

class CMAESOptimizer:
    """
    基于CMA-ES的窗口调度全局优化器（支持可选的并行局部梯度优化）。
    """
    def __init__(
        self,
        num_jobs=2,
        seed=42,
        period_length=100.0,
        bandwidth_capacity=1.0,
        tau=0.1,
        num_windows=5,
        alpha=2.0,
        beta=1.0,
        gamma=0.2,
        mu=0.2,
        lambda1=1,
        lambda2=0.5,
        epsilon=1e-3,
        num_time_points=1000,
        learning_rate=0.75,
        convergence_threshold=1e-8,
        local_opt_steps=50,
        cma_sigma=0.5,
        cma_population=32,
        max_fes=5000,
        results_dir="../results_cmaes",
        device = "cpu",
        use_local_opt=True,     # 是否每次采样后进行局部优化
        n_threads=8
    ):
        self.num_jobs = num_jobs
        self.seed = seed
        self.period_length = period_length
        self.bandwidth_capacity = bandwidth_capacity
        self.tau = tau
        self.num_windows = num_windows
        self.alpha = alpha
        self.beta = beta
        self.gamma = gamma
        self.mu = mu
        self.lambda1 = lambda1
        self.lambda2 = lambda2
        self.epsilon = epsilon
        self.num_time_points = num_time_points
        self.learning_rate = learning_rate
        self.convergence_threshold = convergence_threshold
        self.local_opt_steps = local_opt_steps
        self.cma_sigma = cma_sigma
        self.cma_population = cma_population
        self.max_fes = max_fes
        self.results_dir = results_dir
        self.device = torch.device(device)
        self.use_local_opt = use_local_opt
        self.n_threads = n_threads
        os.makedirs(self.results_dir, exist_ok=True)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.report_file = os.path.join(self.results_dir, f"cmaes_report_{self.timestamp}.txt")
        self.result_file = os.path.join(self.results_dir, f"cmaes_result_{self.timestamp}.json")
        np.random.seed(seed)
        torch.manual_seed(seed)
        self._init_dependencies()

    def _init_dependencies(self):
        data_generator = DataGenerator(random_seed=self.seed)
        dataset = data_generator.generate_test_dataset(num_jobs=self.num_jobs)
        self.communication_patterns = {}
        for job_id, raw_data in dataset.items():
            pattern = CommunicationPattern.from_raw_data(job_id=job_id, raw_data=raw_data)
            pattern = CommunicationPattern.extend_periods(pattern, num_periods=3, components_strategy='scaled')
            pattern.total_period_length = pattern.end_time - pattern.start_time
            self.communication_patterns[job_id] = pattern

        self.scheduler = WindowScheduler(
            bandwidth_capacity=self.bandwidth_capacity,
            tau=self.tau
        )
        for job_id, pattern in self.communication_patterns.items():
            self.scheduler.add_job(job_id=job_id, pattern=pattern)
        self.schedule_init_func = lambda **kw: self.scheduler.initialize_windows(
            num_windows=self.num_windows,
            random_init=True,
            device=self.device,
            transform_type="softplus"
        )

        self.contention_metrics = ContentionMetrics(
            alpha=self.alpha, beta=self.beta, gamma=self.gamma, mu=self.mu
        )
        self.objective_function = ObjectiveFunction(
            scheduler=self.scheduler,
            metrics=self.contention_metrics,
            lambda1=self.lambda1,
            lambda2=self.lambda2,
            epsilon=self.epsilon,
            num_time_points=self.num_time_points
        )
        self.optimizer_builder = self._build_optimizer()

    def _build_optimizer(self):
        def optimizer_fn(flat_param):
            self.scheduler.set_window_params(flat_param)
            optimizer = ScheduleOptimizer(
                objective_function=self.objective_function,
                learning_rate=self.learning_rate,
                max_iterations=self.local_opt_steps,
                convergence_threshold=self.convergence_threshold
            )
            optimizer.initialize_optimizer(flat_param)
            best_param, best_obj, history = optimizer.optimize()
            return best_param.detach().cpu(), float(best_obj), history
        return optimizer_fn

    def _initialize_start_point(self):
        # 随机生成一个可行解
        params = self.schedule_init_func(num_windows=self.num_windows, random_init=True, device=self.device)
        flat_param = params.get_flat_raw_params().detach().clone()
        return flat_param

    def _local_optimize_individual(self, flat_param):
        # 与DE版相同
        local_scheduler = WindowScheduler(
            bandwidth_capacity=self.bandwidth_capacity,
            tau=self.tau
        )
        for job_id, pattern in self.communication_patterns.items():
            local_scheduler.add_job(job_id=job_id, pattern=pattern)
        local_scheduler.initialize_windows(
            num_windows=self.num_windows,
            random_init=True,
            device=self.device,
            transform_type="softplus"
        )
        flat_param = flat_param.to(self.device)
        local_scheduler.window_params.set_flat_raw_params(flat_param)
        local_scheduler.window_params.raw_params = local_scheduler.window_params.raw_params.to(self.device)
        local_objective_function = ObjectiveFunction(
            scheduler=local_scheduler,
            metrics=self.contention_metrics,
            lambda1=self.lambda1,
            lambda2=self.lambda2,
            epsilon=self.epsilon,
            num_time_points=self.num_time_points
        )
        local_optimizer = ScheduleOptimizer(
            objective_function=local_objective_function,
            learning_rate=self.learning_rate,
            max_iterations=self.local_opt_steps,
            visualize=False,
            convergence_threshold=self.convergence_threshold
        )
        local_optimizer.initialize_optimizer()
        best_params, best_obj, _ = local_optimizer.optimize()
        best_flat_param = best_params.get_flat_raw_params().detach().cpu().clone()
        return best_flat_param, float(best_obj)

    def _objective(self, flat_param_np):
        flat_param = torch.tensor(flat_param_np, dtype=torch.float32, device=self.device)
        if self.use_local_opt:
            best_flat_param, best_obj = self._local_optimize_individual(flat_param)
            return best_obj
        else:
            self.scheduler.set_window_params(flat_param)
            obj = self.objective_function(self.scheduler.window_params)
            return float(obj)

    def run(self):
        print("[CMA-ES] Initializing start point ...")
        x0 = self._initialize_start_point().cpu().numpy()
        sigma0 = self.cma_sigma
        opts = {
            'popsize': self.cma_population,
            'seed': self.seed,
            'maxfevals': self.max_fes,
            'verb_disp': 1,
            'verb_log': 0,
        }
        es = cma.CMAEvolutionStrategy(x0, sigma0, opts)
        best_obj = float('inf')
        best_param = None

        generation = 0
        while not es.stop():
            solutions = es.ask()
            # 可并行加速
            if self.use_local_opt and self.n_threads > 1:
                from concurrent.futures import ThreadPoolExecutor
                with ThreadPoolExecutor(max_workers=self.n_threads) as executor:
                    values = list(executor.map(self._objective, solutions))
            else:
                values = [self._objective(x) for x in solutions]
            es.tell(solutions, values)
            es.logger.add()
            es.disp()
            min_obj = min(values)
            min_idx = int(np.argmin(values))
            if min_obj < best_obj:
                best_obj = min_obj
                best_param = solutions[min_idx].copy()
            # 记录日志
            self._report_generation(generation, values, best_obj)
            generation += 1

        print(f"[CMA-ES] Optimization completed. Best objective: {best_obj:.6f}")
        # 应用最优参数到调度器
        best_param_torch = torch.tensor(best_param, dtype=torch.float32, device=self.device)
        self.scheduler.set_window_params(best_param_torch)
        # 保存结果
        result_data = {
            "parameters": {
                "num_jobs": self.num_jobs,
                "num_windows": self.num_windows,
                "bandwidth_capacity": self.bandwidth_capacity,
                "tau": self.tau,
                "alpha": self.alpha,
                "beta": self.beta,
                "gamma": self.gamma,
                "mu": self.mu,
                "lambda1": self.lambda1,
                "lambda2": self.lambda2,
                "epsilon": self.epsilon
            },
            "best_obj": best_obj,
            "best_param": best_param_torch.cpu().numpy().tolist(),
            "history_report": self.report_file,
            "timestamp": self.timestamp
        }
        with open(self.result_file, "w") as f:
            json.dump(result_data, f, indent=2)
        print(f"[CMA-ES] Result saved to {self.result_file}")
        return result_data

    def _report_generation(self, generation, objs, best_obj):
        objs = np.array(objs)
        top10 = np.percentile(objs, 10)
        mean = np.mean(objs)
        std = np.std(objs)
        msg = (
            f"Generation {generation:3d}: "
            f"Best: {best_obj:.6f}, Top10%: {top10:.6f}, Mean: {mean:.6f}, Std: {std:.6f}"
        )
        print(msg)
        with open(self.report_file, "a") as f:
            f.write(msg + "\n")

if __name__ == '__main__':
    optimizer = CMAESOptimizer()
    optimizer.run()