import numpy as np
from typing import Dict, Any, Tuple, Optional, List


class DataGenerator:
    """
    可配置的数据生成器类，用于生成AI任务的通信需求模式
    支持通过超参数自定义生成过程
    """

    def __init__(self,
                 # 基本参数
                 random_seed: int = 42,
                 sample_interval: float = 0.1,
                 min_samples: int = 50,
                 max_samples: int = 100,

                 # 计算阶段参数
                 compute_phase_min_ratio: float = 0.2,
                 compute_phase_max_ratio: float = 0.3,

                 # 通信阶段参数
                 peak_intensity_min: float = 0.8,
                 peak_intensity_max: float = 0.9,
                 rise_ratio_min: float = 0.2,
                 rise_ratio_max: float = 0.3,
                 plateau_ratio_min: float = 0.4,
                 plateau_ratio_max: float = 0.6,
                 fall_ratio_min: float = 0.2,
                 fall_ratio_max: float = 0.3,
                 ):
        """
        初始化数据生成器，设置数据生成的超参数

        Args:
            random_seed: 随机种子，确保实验的可重复性
            sample_interval: 固定采样间隔(调度时隙单位)
            min_samples: 最小采样点数量
            max_samples: 最大采样点数量

            compute_phase_min_ratio: 计算阶段占总时间的最小比例
            compute_phase_max_ratio: 计算阶段占总时间的最大比例

            peak_intensity_min: 峰值通信需求的最小值
            peak_intensity_max: 峰值通信需求的最大值
            rise_ratio_min: 上升阶段占通信阶段的最小比例
            rise_ratio_max: 上升阶段占通信阶段的最大比例
            plateau_ratio_min: 平稳阶段占通信阶段的最小比例
            plateau_ratio_max: 平稳阶段占通信阶段的最大比例
            fall_ratio_min: 下降阶段占通信阶段的最小比例
            fall_ratio_max: 下降阶段占通信阶段的最大比例
        """
        # 设置随机种子
        self.random_seed = random_seed
        np.random.seed(random_seed)

        # 基本参数
        self.sample_interval = sample_interval
        self.min_samples = min_samples
        self.max_samples = max_samples

        # 计算阶段参数
        self.compute_phase_min_ratio = compute_phase_min_ratio
        self.compute_phase_max_ratio = compute_phase_max_ratio

        # 通信阶段参数
        self.peak_intensity_min = peak_intensity_min
        self.peak_intensity_max = peak_intensity_max
        self.rise_ratio_min = rise_ratio_min
        self.rise_ratio_max = rise_ratio_max
        self.plateau_ratio_min = plateau_ratio_min
        self.plateau_ratio_max = plateau_ratio_max
        self.fall_ratio_min = fall_ratio_min
        self.fall_ratio_max = fall_ratio_max

        # 验证参数合法性
        self._validate_parameters()

    def _validate_parameters(self):
        """验证参数的合法性"""
        # 基本范围检查
        assert self.min_samples <= self.max_samples, "最小采样点数必须不大于最大采样点数"
        assert 0 < self.compute_phase_min_ratio <= self.compute_phase_max_ratio < 1, "计算阶段比例必须在(0,1)范围内"
        assert 0 < self.peak_intensity_min <= self.peak_intensity_max, "峰值强度必须为正值"

        # 通信阶段各部分比例总和检查
        min_total_ratio = self.rise_ratio_min + self.plateau_ratio_min + self.fall_ratio_min
        max_total_ratio = self.rise_ratio_max + self.plateau_ratio_max + self.fall_ratio_max

        assert min_total_ratio <= 1.0, "通信阶段最小比例总和不能超过1.0"
        assert max_total_ratio >= 1.0, "通信阶段最大比例总和应至少为1.0"

    def update_parameters(self, **kwargs):
        """
        更新数据生成器的参数

        Args:
            **kwargs: 要更新的参数，键为参数名，值为新的参数值
        """
        for param_name, param_value in kwargs.items():
            if hasattr(self, param_name):
                setattr(self, param_name, param_value)
            else:
                raise ValueError(f"参数 {param_name} 不存在")

                # 重新验证参数
        self._validate_parameters()

        # 重置随机种子
        np.random.seed(self.random_seed)

    def generate_test_dataset(self,
                              num_jobs: int = 10,
                              job_params: Optional[List[Dict[str, Any]]] = None) -> Dict[str, Dict[str, Any]]:
        """
        生成测试数据集，为每个作业创建通信需求模式

        Args:
            num_jobs: 要生成的作业数量
            job_params: 可选，每个作业的自定义参数列表，如果提供，必须与num_jobs长度相同

        Returns:
            dict: 键为作业ID，值为包含需求值和元数据的数据字典
        """
        if job_params is not None:
            assert len(job_params) == num_jobs, "job_params长度必须等于num_jobs"

        dataset = {}

        for job_id in range(num_jobs):
            # 如果提供了作业特定参数，使用它们生成数据
            if job_params is not None:
                job_specific_params = job_params[job_id]
            else:
                job_specific_params = {}

                # 生成作业的通信模式
            demands, start_time, end_time, metadata = self._generate_job_communication_pattern(
                job_id, **job_specific_params)

            # 构建作业数据字典
            job_data = {
                "demands": demands.tolist(),
                "start_time": start_time,
                "end_time": end_time,
                "metadata": metadata
            }

            # 添加到数据集
            dataset[f"job_{job_id}"] = job_data

        return dataset

    def _generate_job_communication_pattern(self,
                                            job_id: int,
                                            **job_params) -> Tuple[np.ndarray, float, float, Dict]:
        """
        为单个作业生成通信模式：计算阶段(需求=0) -> 通信阶段(需求>0) -> 结束(需求=0)

        Args:
            job_id: 作业ID
            **job_params: 该作业的特定参数，会覆盖生成器的默认参数

        Returns:
            tuple: 包含需求值数组、起始时间、结束时间和元数据的元组
        """
        # 合并默认参数和作业特定参数
        params = {
            'min_samples': self.min_samples,
            'max_samples': self.max_samples,
            'compute_phase_min_ratio': self.compute_phase_min_ratio,
            'compute_phase_max_ratio': self.compute_phase_max_ratio,
            'peak_intensity_min': self.peak_intensity_min,
            'peak_intensity_max': self.peak_intensity_max,
            'rise_ratio_min': self.rise_ratio_min,
            'rise_ratio_max': self.rise_ratio_max,
            'plateau_ratio_min': self.plateau_ratio_min,
            'plateau_ratio_max': self.plateau_ratio_max,
            'fall_ratio_min': self.fall_ratio_min,
            'fall_ratio_max': self.fall_ratio_max
        }
        # 更新作业特定参数
        params.update(job_params)

        # 决定总采样点数
        num_samples = np.random.randint(params['min_samples'], params['max_samples'] + 1)

        # 决定计算阶段比例
        compute_ratio = np.random.uniform(params['compute_phase_min_ratio'],
                                          params['compute_phase_max_ratio'])

        # 计算各阶段长度
        compute_length = int(num_samples * compute_ratio)
        comm_length = num_samples - compute_length

        # 确保两个阶段都至少有1个点
        if compute_length < 1:
            compute_length = 1
            comm_length = num_samples - 1

        if comm_length < 1:
            comm_length = 1
            compute_length = num_samples - 1

            # 初始化时间点和需求数组
        start_time = 0.0
        end_time = (num_samples - 1) * self.sample_interval
        demands = np.zeros(num_samples)

        # 为通信阶段生成需求值
        if comm_length > 1:  # 至少需要2个点(一个峰值点和一个结束点)
            # 通信阶段的特征参数
            peak_intensity = np.random.uniform(params['peak_intensity_min'],
                                               params['peak_intensity_max'])

            # 通信阶段各部分比例
            rise_ratio = np.random.uniform(params['rise_ratio_min'],
                                           params['rise_ratio_max'])
            plateau_ratio = np.random.uniform(params['plateau_ratio_min'],
                                              params['plateau_ratio_max'])
            fall_ratio = np.random.uniform(params['fall_ratio_min'],
                                           params['fall_ratio_max'])

            # 归一化比例
            total_ratio = rise_ratio + plateau_ratio + fall_ratio
            rise_ratio /= total_ratio
            plateau_ratio /= total_ratio
            fall_ratio /= total_ratio

            # 计算各部分长度
            rise_length = max(1, int(comm_length * rise_ratio))
            fall_length = max(1, int(comm_length * fall_ratio))
            plateau_length = comm_length - rise_length - fall_length

            # 如果总长度超出，调整
            if rise_length + plateau_length + fall_length > comm_length:
                # 优先调整平稳阶段
                plateau_length = max(1, comm_length - rise_length - fall_length)

                # 计算各阶段的起始索引
            comm_start_idx = compute_length
            rise_end_idx = comm_start_idx + rise_length
            plateau_end_idx = rise_end_idx + plateau_length

            # 生成上升阶段
            for i in range(comm_start_idx, rise_end_idx):
                progress = (i - comm_start_idx) / rise_length
                demands[i] = peak_intensity * (progress ** 1.5)  # 使用幂函数模拟上升

            # 生成平稳阶段
            if plateau_length > 0:
                # 添加小波动
                plateau_noise = np.random.normal(0, 0.05, size=plateau_length)
                plateau_demands = peak_intensity * (1 + plateau_noise)
                # 确保在合理范围内
                plateau_demands = np.clip(plateau_demands, peak_intensity * 0.9, peak_intensity * 1.1)
                demands[rise_end_idx:plateau_end_idx] = plateau_demands

                # 生成下降阶段
            for i in range(plateau_end_idx, num_samples - 1):
                progress = (i - plateau_end_idx) / (num_samples - 1 - plateau_end_idx)
                demands[i] = peak_intensity * (1 - progress ** 1.2)  # 使用幂函数模拟下降

        # 确保最后一个点的需求为0
        demands[-1] = 0.0

        # 构建元数据
        metadata = {
            "job_id": job_id,
            "sample_interval": self.sample_interval,
            "num_samples": num_samples,
            "compute_phase_length": compute_length,
            "comm_phase_length": comm_length
        }

        # 添加作业特定参数到元数据
        for key, value in job_params.items():
            metadata[f"custom_{key}"] = value

        return demands, start_time, end_time, metadata