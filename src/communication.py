import json
import math
import os

import numpy as np
import torch

from matplotlib import pyplot as plt
from matplotlib.gridspec import GridSpec

from src.nonnegative_transforms import softplus_forward, softplus_inverse, modified_softplus_forward, \
    modified_softplus_inverse


class CommunicationPattern:
    """
    通信模式类，基于离散傅里叶变换(DFT)将离散通信需求数据转换为连续可微函数

    使用非负变换确保通信需求始终为非负值。
    遵循公式：f̂_j(t) = ∑ k=0^K A_k cos(2πkt/T_j) + B_k sin(2πkt/T_j)
    其中：
    - A_k, B_k 为傅里叶系数，表示对应频率成分的幅度
    - T_j 是任务生命周期的长度
    - K 是选择的频率成分数量
    """

    def __init__(self, job_id, num_components=None, window_smoothness=0.01, transform_method='softplus'):
        """
        初始化通信模式对象

        Args:
            job_id (str): 作业ID
            num_components (int, optional): 频率成分数量K。若为None，自动设置为采样点数/2
            window_smoothness (float): 窗口函数平滑参数
            transform_method (str): 数据变换方法，默认为'softplus'
        """
        # 基本标识信息
        self.job_id = job_id
        self.num_components = num_components
        self.window_smoothness = window_smoothness
        self.transform_method = transform_method

        # 初始化各种属性（确保所有属性都在初始化时定义）
        self.fitted = False

        # 时间相关属性
        self.start_time = 0.0
        self.end_time = 0.0
        self.period_length = 0.0
        self.total_period_length = 0.0
        self.delay = 0.0

        # 数据相关属性
        self.demands = None           # 原始需求数据
        self.time_points = None       # 对应的时间点
        self.transformed_demands = None  # 变换后的需求数据
        self.fourier_coeffs = None    # 傅里叶系数

        # 元数据
        self.metadata = {}

        # 变换参数
        self.transform_params = {'offset': 0.0}

        # 设备配置
        # self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.device = torch.device('cpu')

        # 设置变换函数
        self._setup_transform_functions()

    def save_dft_coefficients(self, filepath=None):
        """保存傅里叶系数到文件"""
        if not self.fitted:
            print(f"警告: 作业 {self.job_id} 未完成拟合，无法保存系数")
            return False

        if filepath is None:
            # 创建缓存目录（如果不存在）
            os.makedirs("dft_cache", exist_ok=True)
            filepath = f"dft_cache/dft_coeffs_{self.job_id}.pt"

        save_data = {
            'fourier_coeffs': self.fourier_coeffs,
            'transform_params': self.transform_params,
            'period_length': self.period_length,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'num_components': self.num_components,
            'window_smoothness': self.window_smoothness,
            'transform_method': self.transform_method,
            'metadata': self.metadata
        }
        torch.save(save_data, filepath)
        print(f"作业 {self.job_id}: 傅里叶系数已保存到 {filepath}")
        return True

    def load_dft_coefficients(self, filepath=None):
        """从文件加载傅里叶系数"""
        if filepath is None:
            filepath = f"dft_cache/dft_coeffs_{self.job_id}.pt"

        if os.path.exists(filepath):
            try:
                data = torch.load(filepath, map_location=self.device)

                # 加载基本属性
                self.fourier_coeffs = data['fourier_coeffs']
                self.transform_params = data['transform_params']
                self.period_length = data['period_length']
                self.start_time = data['start_time']
                self.end_time = data['end_time']
                self.num_components = data['num_components']

                # 加载可选属性（如果存在）
                if 'window_smoothness' in data:
                    self.window_smoothness = data['window_smoothness']
                if 'transform_method' in data:
                    self.transform_method = data['transform_method']
                    # 重新设置变换函数
                    self._setup_transform_functions()
                if 'metadata' in data:
                    self.metadata = data['metadata']

                self.fitted = True
                print(f"作业 {self.job_id}: 已从 {filepath} 加载傅里叶系数")
                return True
            except Exception as e:
                print(f"加载傅里叶系数时出错: {e}")
                return False
        else:
            print(f"未找到缓存文件: {filepath}")
            return False
    def _setup_transform_functions(self):
        # Bug 所在：
        '''

        softplus是一个平滑的激活函数：f(x) = log(1 + e^x)
        当x很大时，它接近于x；当x很小（负值）时，它接近于0
        但关键是，当x=0时，softplus(0) = log(1 + e^0) = log(2) ≈ 0.693...

        '''
        """设置使用的变换函数"""
        """设置使用的变换函数"""
        if self.transform_method == 'softplus':
            self.forward_transform = softplus_forward
            self.inverse_transform = softplus_inverse
        elif self.transform_method == 'original_softplus':
            # 保留原始softplus作为选项
            self.forward_transform = lambda x: torch.log(1 + torch.exp(x))
            self.inverse_transform = lambda y: torch.log(torch.exp(y) - 1)
        else:
            # 默认使用softplus
            self.transform_method = 'softplus'
            self.forward_transform = softplus_forward
            self.inverse_transform = softplus_inverse
            print(f"Unknown transform method '{self.transform_method}', falling back to 'softplus'")


    @classmethod
    def from_raw_data(cls, job_id, raw_data, num_components=None, transform_method='softplus'):
        """
        从原始数据创建通信模式对象的静态方法

        Args:
            job_id (str): 作业ID
            raw_data (dict): 原始通信需求数据，应包含demands, start_time, end_time和metadata
            num_components (int, optional): 频率成分数量K
            transform_method (str): 数据变换方法，默认为'softplus'

        Returns:
            CommunicationPattern: 初始化并拟合好的通信模式对象
        """
        # 创建实例
        pattern = cls(job_id, num_components=num_components, transform_method=transform_method)

        # 存储原始数据
        pattern.demands = torch.tensor(raw_data['demands'], dtype=torch.float32, device=pattern.device)
        pattern.start_time = raw_data['start_time']
        pattern.end_time = raw_data['end_time']
        pattern.metadata = raw_data.get('metadata', {})

        # 设置周期长度T_j为数据的时间跨度
        pattern.period_length = pattern.end_time - pattern.start_time

        # 创建时间点
        pattern.time_points = torch.linspace(
            pattern.start_time,
            pattern.end_time,
            len(pattern.demands),
            device=pattern.device
        )

        # 执行傅里叶分析
        pattern.fit_dft()

        return pattern

    @classmethod
    def extend_periods(cls, original_pattern, num_periods=3, noise_level=0.0,
                      components_strategy='scaled', custom_components=None,
                      max_components=1000):
        """
        基于原始模式创建延长到多个周期的新模式对象

        Args:
            original_pattern: 原始CommunicationPattern对象
            num_periods (int): 延长的周期数
            noise_level (float): 添加到延长周期的噪声水平（0.0表示完美复制）
            components_strategy (str): 设置频率分量的策略:
                - 'original': 保持与原始模式相同的分量数
                - 'scaled': 与周期数线性缩放（默认）
                - 'sublinear': 考虑重复模式的谱特性，进行次线性缩放
                - 'nyquist': 基于样本数量的奈奎斯特限制
                - 'custom': 使用custom_components参数
            custom_components (int): 使用components_strategy='custom'时的自定义分量数
            max_components (int): 分量数的最大限制，防止过度计算

        Returns:
            CommunicationPattern: 延长的模式对象
        """
        if not original_pattern.fitted:
            raise ValueError(f"原始模式 {original_pattern.job_id} 未拟合，请先执行fit_dft方法")

        # 创建新的模式ID
        extended_job_id = f"{original_pattern.job_id}_extended_{num_periods}periods"

        # 确定适当的频率分量数
        original_components = original_pattern.num_components

        # 根据选择的策略设置分量数
        if components_strategy == 'original':
            # 保持与原始模式相同的分量数
            num_components = original_components

        elif components_strategy == 'scaled':
            # 与周期数线性缩放
            num_components = original_components * num_periods

        elif components_strategy == 'sublinear':
            # 次线性缩放，考虑重复模式的谱特性
            num_components = int(original_components * (1 + math.log(num_periods)))

        elif components_strategy == 'nyquist':
            # 基于延长的样本数量（奈奎斯特限制）
            extended_samples = len(original_pattern.demands) * num_periods
            num_components = extended_samples // 2

        elif components_strategy == 'custom' and custom_components is not None:
            # 使用自定义分量数
            num_components = custom_components

        else:
            # 默认线性缩放
            num_components = original_components * num_periods
            print(f"警告: 使用默认缩放策略（{num_components} 分量）")

        # 应用最大限制
        if max_components and num_components > max_components:
            original_num_components = num_components
            num_components = max_components
            print(f"警告: 将分量数从 {original_num_components} 减少到最大限制 {max_components}")

        # 创建新实例
        extended_pattern = cls(extended_job_id,
                             num_components=num_components,
                             window_smoothness=original_pattern.window_smoothness,
                             transform_method=original_pattern.transform_method)

        # 复制基本属性
        extended_pattern.start_time = original_pattern.start_time
        original_period_length = original_pattern.period_length
        extended_pattern.period_length = original_period_length * num_periods
        extended_pattern.end_time = extended_pattern.start_time + extended_pattern.period_length

        # 创建延长的需求数据
        original_demands = original_pattern.demands.cpu().numpy()

        if noise_level > 0:
            # 生成带噪声的周期
            extended_demands = []
            for i in range(num_periods):
                # 添加随机噪声
                period_demands = original_demands + np.random.normal(0, noise_level, size=len(original_demands))
                # 确保需求非负
                period_demands = np.maximum(period_demands, 0)
                extended_demands.append(period_demands)
            extended_demands = np.concatenate(extended_demands)
        else:
            # 完美复制原始周期
            extended_demands = np.tile(original_demands, num_periods)

        # 转换为张量
        extended_pattern.demands = torch.tensor(extended_demands, dtype=torch.float32, device=extended_pattern.device)

        # 创建延长的时间点
        extended_pattern.time_points = torch.linspace(
            extended_pattern.start_time,
            extended_pattern.end_time,
            len(extended_pattern.demands),
            device=extended_pattern.device
        )

        # 复制并更新元数据
        extended_pattern.metadata = original_pattern.metadata.copy() if original_pattern.metadata else {}
        extended_pattern.metadata['original_job_id'] = original_pattern.job_id
        extended_pattern.metadata['num_periods'] = num_periods
        extended_pattern.metadata['original_period_length'] = original_period_length
        extended_pattern.metadata['noise_level'] = noise_level
        extended_pattern.metadata['components_strategy'] = components_strategy
        extended_pattern.metadata['original_components'] = original_components
        extended_pattern.metadata['extended_components'] = num_components

        # 如果元数据中存在，更新计算阶段和通信阶段长度
        if 'compute_phase_length' in extended_pattern.metadata:
            extended_pattern.metadata['compute_phase_length'] *= num_periods
        if 'comm_phase_length' in extended_pattern.metadata:
            extended_pattern.metadata['comm_phase_length'] *= num_periods

        # 打印分量设置信息
        print(f"将模式从1个周期延长到{num_periods}个周期:")
        print(f"  - 策略: {components_strategy}")
        print(f"  - 原始分量数: {original_components}")
        print(f"  - 延长分量数: {num_components}")
        print(f"  - 数据点数: {len(extended_demands)}")

        # 执行傅里叶分析
        extended_pattern.fit_dft()

        return extended_pattern

    def extend_self(self, num_periods=3, noise_level=0.0, components_strategy='scaled',
                   custom_components=None, max_components=1000):
        """
        将当前模式延长到多个周期，修改自身

        Args:
            num_periods (int): 延长的周期数
            noise_level (float): 添加到延长周期的噪声水平
            components_strategy (str): 设置频率分量的策略
            custom_components (int): 使用'custom'策略时的自定义分量数
            max_components (int): 分量数的最大限制

        Returns:
            self: 更新后的当前对象
        """
        if not self.fitted:
            raise ValueError(f"模式 {self.job_id} 未拟合，请先执行fit_dft方法")

        # 保存原始参数
        original_job_id = self.job_id
        original_period_length = self.period_length
        original_demands = self.demands.cpu().numpy()
        original_components = self.num_components

        # 更新作业ID
        self.job_id = f"{original_job_id}_extended_{num_periods}periods"

        # 确定适当的频率分量数
        if components_strategy == 'original':
            num_components = original_components

        elif components_strategy == 'scaled':
            num_components = original_components * num_periods

        elif components_strategy == 'sublinear':
            num_components = int(original_components * (1 + math.log(num_periods)))

        elif components_strategy == 'nyquist':
            extended_samples = len(original_demands) * num_periods
            num_components = extended_samples // 2

        elif components_strategy == 'custom' and custom_components is not None:
            num_components = custom_components

        else:
            num_components = original_components * num_periods
            print(f"警告: 使用默认缩放策略（{num_components} 分量）")

        # 应用最大限制
        if max_components and num_components > max_components:
            original_num_components = num_components
            num_components = max_components
            print(f"警告: 将分量数从 {original_num_components} 减少到最大限制 {max_components}")

        # 更新周期长度和结束时间
        self.period_length *= num_periods
        self.end_time = self.start_time + self.period_length

        # 更新分量数
        self.num_components = num_components

        # 创建延长的需求数据
        if noise_level > 0:
            extended_demands = []
            for i in range(num_periods):
                period_demands = original_demands + np.random.normal(0, noise_level, size=len(original_demands))
                period_demands = np.maximum(period_demands, 0)
                extended_demands.append(period_demands)
            extended_demands = np.concatenate(extended_demands)
        else:
            extended_demands = np.tile(original_demands, num_periods)

        # 更新需求数据
        self.demands = torch.tensor(extended_demands, dtype=torch.float32, device=self.device)

        # 更新时间点
        self.time_points = torch.linspace(
            self.start_time,
            self.end_time,
            len(self.demands),
            device=self.device
        )

        # 更新元数据
        if not hasattr(self, 'metadata') or self.metadata is None:
            self.metadata = {}
        self.metadata['original_job_id'] = original_job_id
        self.metadata['num_periods'] = num_periods
        self.metadata['original_period_length'] = original_period_length
        self.metadata['noise_level'] = noise_level
        self.metadata['components_strategy'] = components_strategy
        self.metadata['original_components'] = original_components
        self.metadata['extended_components'] = num_components

        # 如果存在，更新计算和通信阶段长度
        if 'compute_phase_length' in self.metadata:
            self.metadata['compute_phase_length'] *= num_periods
        if 'comm_phase_length' in self.metadata:
            self.metadata['comm_phase_length'] *= num_periods

        # 打印分量设置信息
        print(f"将模式从1个周期延长到{num_periods}个周期:")
        print(f"  - 策略: {components_strategy}")
        print(f"  - 原始分量数: {original_components}")
        print(f"  - 延长分量数: {num_components}")
        print(f"  - 数据点数: {len(extended_demands)}")

        # 重新执行傅里叶分析
        self.fit_dft()

        return self

    def fit_dft(self, times=None, demands=None, force_refit=False):
        """
        使用离散傅里叶变换拟合通信需求数据

        Args:
            times: 可选的时间点数据，如果不提供则使用self.time_points
            demands: 可选的需求数据，如果不提供则使用self.demands
            force_refit: 是否强制重新拟合，即使存在缓存
        """
        # 设置缓存文件路径

        cache_file = f"dft_cache/dft_coeffs_{self.job_id}.pt"

        # 尝试加载缓存（除非强制重新拟合）
        if not force_refit and os.path.exists(cache_file):
            if self.load_dft_coefficients(cache_file):
                # 加载成功，无需再拟合
                return self.fourier_coeffs

                # 如果提供了新数据，则更新
        if times is not None:
            self.time_points = times if isinstance(times, torch.Tensor) else torch.tensor(times, dtype=torch.float32,
                                                                                          device=self.device)
        if demands is not None:
            self.demands = demands if isinstance(demands, torch.Tensor) else torch.tensor(demands, dtype=torch.float32,
                                                                                          device=self.device)

        if self.demands is None or len(self.demands) < 2:
            raise ValueError(f"作业 {self.job_id}: 傅里叶分析至少需要2个需求样本")

            # 移动数据到设备
        self.demands = self.demands.to(self.device)
        self.time_points = self.time_points.to(self.device)

        # 确保所有需求值为正
        min_val = torch.min(self.demands)
        if min_val <= 0:
            self.transform_params['offset'] = float(abs(min_val) + 1e-4)
        else:
            self.transform_params['offset'] = 0.0

        print(f"作业 {self.job_id}: 使用 {self.transform_method} 变换，偏移量 {self.transform_params['offset']}")

        # 应用非负变换的逆变换，将原始数据转换为内部表示
        adjusted_demands = self.demands + self.transform_params['offset']
        self.transformed_demands = self.inverse_transform(adjusted_demands)

        print(
            f"作业 {self.job_id}: 原始需求范围 [{torch.min(self.demands).item():.4f}, {torch.max(self.demands).item():.4f}]")
        print(
            f"作业 {self.job_id}: 变换后范围 [{torch.min(self.transformed_demands).item():.4f}, {torch.max(self.transformed_demands).item():.4f}]")

        # 确定分量数量K：如果未指定，使用奈奎斯特采样定理设为N/2
        n = len(self.demands)
        if self.num_components is None:
            self.num_components = n // 2
            print(f"作业 {self.job_id}: 自动设置频率分量为 {self.num_components} (N/2)")

            # 使用优化方法拟合傅里叶系数
        self._optimize_fourier_coefficients()

        # 拟合完成后保存结果
        self.fitted = True
        self.save_dft_coefficients(cache_file)

        return self.fourier_coeffs

    def _optimize_fourier_coefficients(self):
        """
        使用优化方法直接拟合傅里叶系数，确保高精度拟合
        """
        # 归一化时间，使其范围为[0, 2π]
        normalized_time = 2 * torch.pi * (self.time_points - self.start_time) / self.period_length

        # 初始化傅里叶系数 A_k 和 B_k
        k_max = self.num_components
        a_coeffs = torch.zeros(k_max + 1, dtype=torch.float32, device=self.device, requires_grad=True)
        b_coeffs = torch.zeros(k_max + 1, dtype=torch.float32, device=self.device, requires_grad=True)

        # 设置A_0为平均值（直流分量）
        with torch.no_grad():
            a_coeffs[0] = torch.mean(self.transformed_demands)  # 使用变换后的数据

        # 使用Adam优化器
        optimizer = torch.optim.Adam([a_coeffs, b_coeffs], lr=0.01)
        # optimizer = torch.optim.SGD([a_coeffs, b_coeffs], lr=0.01)

        # 优化步骤
        n_iterations = 2000
        for i in range(n_iterations):
            optimizer.zero_grad()

            # 计算傅里叶级数
            pred = a_coeffs[0].clone()  # A_0项
            for k in range(1, k_max + 1):
                pred = pred + a_coeffs[k] * torch.cos(k * normalized_time) + b_coeffs[k] * torch.sin(k * normalized_time)

            # 计算均方误差损失（在变换空间中）
            loss = torch.mean((pred - self.transformed_demands) ** 2)

            # 反向传播
            loss.backward()
            optimizer.step()

            # 打印进度（每500次迭代）
            if i % 500 == 0 or i == n_iterations - 1:
                print(f"作业 {self.job_id}, 迭代 {i}, 损失: {loss.item():.8f}")

        # 使用L-BFGS进行精细优化
        a_coeffs_lbfgs = a_coeffs.clone().detach().requires_grad_(True)
        b_coeffs_lbfgs = b_coeffs.clone().detach().requires_grad_(True)

        optimizer = torch.optim.LBFGS([a_coeffs_lbfgs, b_coeffs_lbfgs],
                                      lr=0.1,
                                      max_iter=50,
                                      line_search_fn='strong_wolfe')

        def closure():
            optimizer.zero_grad()

            # 计算傅里叶级数
            pred = a_coeffs_lbfgs[0].clone()
            for k in range(1, k_max + 1):
                pred = pred + a_coeffs_lbfgs[k] * torch.cos(k * normalized_time) + b_coeffs_lbfgs[k] * torch.sin(k * normalized_time)

            # 计算损失
            loss = torch.mean((pred - self.transformed_demands) ** 2)
            loss.backward()
            return loss

        try:
            print(f"作业 {self.job_id}: 使用L-BFGS进行精细调整...")
            optimizer.step(closure)

            # 存储最终的傅里叶系数
            self.fourier_coeffs = {
                # 'a': a_coeffs_lbfgs.detach(),
                # 'b': b_coeffs_lbfgs.detach()
                'a': a_coeffs_lbfgs,
                'b': b_coeffs_lbfgs
            }

            # 计算最终MSE（在变换空间和原始空间）
            with torch.no_grad():
                # 变换空间拟合误差
                pred = a_coeffs_lbfgs[0].clone()
                for k in range(1, k_max + 1):
                    pred = pred + a_coeffs_lbfgs[k] * torch.cos(k * normalized_time) + b_coeffs_lbfgs[k] * torch.sin(k * normalized_time)

                transformed_mse = torch.mean((pred - self.transformed_demands) ** 2).item()

                # 原始空间拟合误差（反变换回原始空间）
                orig_pred = self.forward_transform(pred) - self.transform_params['offset']
                orig_mse = torch.mean((orig_pred - self.demands) ** 2).item()

                print(f"作业 {self.job_id} 拟合完成:")
                print(f"  变换空间MSE: {transformed_mse:.8f}")
                print(f"  原始空间MSE: {orig_mse:.8f}")
                print(f"  分量数: {self.num_components}")
        except Exception as e:
            print(f"L-BFGS优化失败，使用Adam结果。错误: {e}")
            self.fourier_coeffs = {
                'a': a_coeffs.detach(),
                'b': b_coeffs.detach()
            }

    def evaluate_at_time(self, t):
        """
        评估给定时间点的通信需求值 f̂_j(t)

        Args:
            t (float or torch.Tensor): 单个时间点

        Returns:
            torch.Tensor: 通信需求值
        """
        # 确保输入是张量
        if not isinstance(t, torch.Tensor):
            t = torch.tensor(t, dtype=torch.float32, device=self.device)

        # 转换为批量格式进行计算
        if t.dim() == 0:  # 标量张量
            t = t.unsqueeze(0)
            result = self.evaluate_batch(t,True)
            return result[0]  # 返回标量结果
        else:
            # 已经是向量，直接使用批量评估
            return self.evaluate_batch(t,True)

    def evaluate_batch(self, t_batch,windows=True):
        """
        批量评估多个时间点的通信需求值

        使用向量化计算提高效率，一次性计算所有时间点的傅里叶级数。
        保留完整的梯度流，以支持对时间参数的优化。

        Args:
            t_batch (torch.Tensor): 时间点张量，形状为 [batch_size]

        Returns:
            torch.Tensor: 通信需求值张量，形状为 [batch_size]
        """
        if not self.fitted:
            raise RuntimeError(f"作业 {self.job_id}: 请先执行fit_dft方法")

        # 确保输入是张量并且在正确的设备上
        if not isinstance(t_batch, torch.Tensor):
            t_batch = torch.tensor(t_batch, dtype=torch.float32, device=self.device)
        elif t_batch.device != self.device:
            t_batch = t_batch.to(self.device)

        # 确保输入是批量形式
        if t_batch.dim() == 0:
            t_batch = t_batch.unsqueeze(0)

        # 归一化时间，使其范围在 [0, 2π]
        normalized_t = 2 * torch.pi * (t_batch - self.start_time) / self.period_length  # [batch_size]

        # with torch.no_grad():
        # 获取傅里叶系数（这些是固定参数，不需要跟踪梯度）
        a_coeffs = self.fourier_coeffs['a']  # [num_components + 1]
        b_coeffs = self.fourier_coeffs['b']  # [num_components + 1]

        # 创建频率索引向量（这是常数，不需要梯度）
        k_indices = torch.arange(1, len(a_coeffs), device=self.device)  # [num_components]

        # 扩展维度以便广播
        k_indices = k_indices.unsqueeze(1)  # [num_components, 1]
        normalized_t_expanded = normalized_t.unsqueeze(0)  # [1, batch_size]

        # 计算所有频率的正弦和余弦项
        # 保留梯度，因为t_batch可能需要梯度
        cos_terms = torch.cos(k_indices * normalized_t_expanded)  # [num_components, batch_size]
        sin_terms = torch.sin(k_indices * normalized_t_expanded)  # [num_components, batch_size]

        # 应用傅里叶系数
        # 系数扩展为 [num_components, 1] 以便与 [num_components, batch_size] 相乘
        a_contribution = (a_coeffs[1:].unsqueeze(1) * cos_terms).sum(dim=0)  # [batch_size]
        b_contribution = (b_coeffs[1:].unsqueeze(1) * sin_terms).sum(dim=0)  # [batch_size]

        # 加上直流分量 A_0
        result = a_coeffs[0] + a_contribution + b_contribution  # [batch_size]


        # # 计算窗口函数并应用（保留梯度）
        # window_values = self.window_function_batch(t_batch)  # [batch_size]
        # windowed_result = result * window_values  # [batch_size]
        #
        # # 应用正向变换返回原始空间并减去偏移量
        # offset = self.transform_params.get('offset', 0.0)
        # final_result = self.forward_transform(windowed_result) - offset  # [batch_size]



        # 应用正向变换返回原始空间并减去偏移量
        offset = self.transform_params.get('offset', 0.0)
        transformed_result = self.forward_transform(result) - offset  # [batch_size]

        # 计算窗口函数并应用（保留梯度）
        window_values = self.window_function_batch(t_batch)  # [batch_size]
        windowed_result = transformed_result * window_values  # [batch_size]


        return windowed_result


    def window_function(self, t):
        """
        窗口函数，限制通信需求在有效时间范围内

        Args:
            t (float or torch.Tensor): 单个时间点或时间点张量

        Returns:
            torch.Tensor: 窗口系数，范围为[0,1]，形状与输入一致
        """
        # 确保输入是张量并在正确的设备上
        if not isinstance(t, torch.Tensor):
            t = torch.tensor(t, dtype=torch.float32, device=self.device)
        elif t.device != self.device:
            t = t.to(self.device)

        # 保存原始输入的维度信息，以便正确处理单个值
        is_scalar = t.dim() == 0
        if is_scalar:
            t = t.unsqueeze(0)

        tau = self.window_smoothness
        t_start = self.start_time
        t_end = self.end_time + self.delay

        # 计算sigmoid边缘
        left_edge = torch.sigmoid((t - t_start) / tau)
        right_edge = 1.0 - torch.sigmoid((t - t_end) / tau)

        result = left_edge * right_edge

        # 如果输入是标量，返回标量结果
        if is_scalar:
            return result.squeeze(0)
        return result

    def window_function_batch(self, t_batch):
        """
        批量计算窗口函数值

        Args:
            t_batch (torch.Tensor): 时间点张量，形状为 [batch_size]

        Returns:
            torch.Tensor: 窗口函数值张量，形状为 [batch_size]
        """
        return self.window_function(t_batch)

    def get_fourier_components(self):
        """
        获取傅里叶系数A_k和B_k

        Returns:
            dict: 包含'a'和'b'系数的字典
        """
        if not self.fitted:
            raise RuntimeError(f"作业 {self.job_id}: 请先执行fit_dft方法")

        return {
            'a': self.fourier_coeffs['a'].cpu().detach().numpy(),
            'b': self.fourier_coeffs['b'].cpu().detach().numpy()
        }


    def visualize_original_vs_extended(self, original_pattern=None, output_dir=None):
        """
        可视化原始模式和延长模式的比较
        如果未提供原始模式，则假设当前模式是延长的

        Args:
            original_pattern: 原始CommunicationPattern对象（可选）
            output_dir: 输出目录（可选）
        """
        is_extended = 'num_periods' in self.metadata

        if original_pattern is None and not is_extended:
            raise ValueError("Must provide original pattern, or current pattern must be extended")

        # 确定要比较的模式
        extended_pattern = self

        if original_pattern is None:
            # 尝试从元数据获取原始作业ID
            if 'original_job_id' not in self.metadata:
                raise ValueError("Original pattern not provided and metadata lacks original_job_id")

            print("Warning: Original pattern not provided, only showing extended pattern")

        num_periods = self.metadata.get('num_periods', 3)

        # 创建图形
        plt.figure(figsize=(15, 10))

        if original_pattern:
            # 1. 原始数据和拟合
            plt.subplot(2, 1, 1)

            # 原始数据
            original_demands = original_pattern.demands.cpu().numpy()
            original_time = np.linspace(original_pattern.start_time,
                                        original_pattern.end_time,
                                        len(original_demands))

            # 原始模式拟合
            fine_time_original = torch.linspace(original_pattern.start_time,
                                                original_pattern.end_time,
                                                1000,
                                                device=original_pattern.device)
            fitted_original = original_pattern.evaluate_batch(fine_time_original,True).cpu().detach().numpy()

            plt.plot(original_time, original_demands, 'bo', markersize=3, alpha=0.6, label='Original Data')
            plt.plot(fine_time_original.cpu().numpy(), fitted_original, 'r-', linewidth=1.5,
                     label='Fourier Fit (Single Period)')
            plt.title(f"Original Communication Pattern - {original_pattern.job_id}")
            plt.xlabel("Time")
            plt.ylabel("Communication Demand")
            plt.grid(True, alpha=0.3)
            plt.legend()

            subplot_index = 2
        else:
            subplot_index = 1

        # 2. 延长数据和拟合
        plt.subplot(2, 1, subplot_index)

        # 延长数据
        extended_demands = extended_pattern.demands.cpu().numpy()
        extended_time = np.linspace(extended_pattern.start_time,
                                    extended_pattern.end_time,
                                    len(extended_demands))

        # 延长模式拟合
        fine_time_extended = torch.linspace(extended_pattern.start_time,
                                            extended_pattern.end_time,
                                            3000,
                                            device=extended_pattern.device)
        fitted_extended = extended_pattern.evaluate_batch(fine_time_extended,True).cpu().detach().numpy()

        plt.plot(extended_time, extended_demands, 'bo', markersize=3, alpha=0.4, label='Extended Data')
        plt.plot(fine_time_extended.cpu().numpy(), fitted_extended, 'g-', linewidth=1.5,
                 label=f'Fourier Fit ({num_periods} Periods)')

        # 添加周期边界线
        original_period_length = extended_pattern.metadata.get('original_period_length',
                                                               extended_pattern.period_length / num_periods)
        for i in range(1, num_periods):
            period_boundary = extended_pattern.start_time + i * original_period_length
            plt.axvline(x=period_boundary, color='purple', linestyle='--', alpha=0.7)
            plt.text(period_boundary, max(extended_demands) * 0.9, f'Period {i}->{i + 1}',
                     rotation=90, ha='right', va='top')

        plt.title(f"Extended to {num_periods} Periods")
        plt.xlabel("Time")
        plt.ylabel("Communication Demand")
        plt.grid(True, alpha=0.3)
        plt.legend()

        # 添加全局标题
        strategy = self.metadata.get('components_strategy', 'unknown')
        components = self.metadata.get('extended_components', self.num_components)
        plt.suptitle(
            f"Communication Pattern Period Extension Analysis - {extended_pattern.job_id}\nStrategy: {strategy}, Components: {components}",
            fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.94])

        # 保存图形
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(f"{output_dir}/{extended_pattern.job_id}_period_extension.png", dpi=300)
            print(f"Period extension analysis saved to {output_dir}/{extended_pattern.job_id}_period_extension.png")

        plt.close()


    def visualize_spectrum(self, output_dir=None):
        """
        可视化傅里叶系数谱

        Args:
            output_dir: 输出目录（可选）
        """
        # 获取傅里叶系数
        coeffs = self.get_fourier_components()

        # 创建图形
        plt.figure(figsize=(12, 8))

        # 振幅谱
        amplitude = np.sqrt(coeffs['a'] ** 2 + coeffs['b'] ** 2)

        # 创建网格布局
        gs = GridSpec(2, 2, width_ratios=[3, 1], height_ratios=[1, 1])

        # 振幅谱（线形图）
        ax1 = plt.subplot(gs[0, 0])
        ax1.plot(np.arange(len(amplitude)), amplitude, 'b-', linewidth=1.5)
        ax1.set_title(f"{self.job_id} - Fourier Coefficient Amplitude Spectrum")
        ax1.set_xlabel("Frequency Component k")
        ax1.set_ylabel("Amplitude |A_k, B_k|")
        ax1.grid(True, alpha=0.3)

        # 振幅谱（棒状图，仅显示前20个分量）
        max_display = min(20, len(amplitude))
        ax2 = plt.subplot(gs[0, 1])
        ax2.stem(np.arange(max_display), amplitude[:max_display])
        ax2.set_title(f"Top {max_display} Components")
        ax2.set_xlabel("k")
        ax2.set_ylabel("Amplitude")
        ax2.grid(True, alpha=0.3)

        # 相位谱
        phase = np.arctan2(coeffs['b'], coeffs['a'])

        ax3 = plt.subplot(gs[1, 0])
        ax3.plot(np.arange(len(phase)), phase, 'r-', linewidth=1.5)
        ax3.set_title("Phase Spectrum")
        ax3.set_xlabel("Frequency Component k")
        ax3.set_ylabel("Phase (radians)")
        ax3.grid(True, alpha=0.3)

        # A和B系数分布
        ax4 = plt.subplot(gs[1, 1])
        ax4.scatter(coeffs['a'][:max_display], coeffs['b'][:max_display],
                    c=np.arange(max_display), cmap='viridis', alpha=0.7)
        for i in range(max_display):
            ax4.annotate(f"{i}", (coeffs['a'][i], coeffs['b'][i]), fontsize=8)
        ax4.set_title("A_k vs B_k")
        ax4.set_xlabel("A_k")
        ax4.set_ylabel("B_k")
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='k', linestyle='-', alpha=0.3)
        ax4.axvline(x=0, color='k', linestyle='-', alpha=0.3)

        # 添加关于分量策略的元数据（如果可用）
        if 'components_strategy' in self.metadata:
            strategy = self.metadata['components_strategy']
            original = self.metadata.get('original_components', 'N/A')
            extended = self.metadata.get('extended_components', self.num_components)
            periods = self.metadata.get('num_periods', 1)

            plt.figtext(0.5, 0.01,
                        f"Strategy: {strategy}, Periods: {periods}, Components: {extended} (Original: {original})",
                        ha='center', fontsize=10, bbox={"facecolor": "lightgray", "alpha": 0.5, "pad": 5})

        plt.tight_layout(rect=[0, 0.03, 1, 1])

        # 保存图形
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
            plt.savefig(f"{output_dir}/{self.job_id}_fourier_spectrum.png", dpi=300)
            print(f"Spectrum analysis saved to {output_dir}/{self.job_id}_fourier_spectrum.png")

        plt.close()


    def __str__(self):
        """返回模式描述"""
        num_periods_str = ""
        if hasattr(self, 'metadata') and self.metadata and 'num_periods' in self.metadata:
            num_periods_str = f", periods={self.metadata['num_periods']}"

        return (f"CommunicationPattern(job_id={self.job_id}, "
                f"period_length={self.period_length:.2f}, "
                f"components={self.num_components}, "
                f"transform={self.transform_method}, "
                f"duration={self.end_time - self.start_time:.2f}{num_periods_str})")

if __name__ == '__main__':
    # 设置随机种子以确保可复现性
    torch.manual_seed(42)
    np.random.seed(42)

    # 加载JSON数据集
    with open("communication_dataset.json", 'r') as file:
        dataset = json.load(file)

        # 获取第一个作业
    job_id, job_data = next(iter(dataset.items()))

    # 1. 创建原始通信模式
    original_pattern = CommunicationPattern.from_raw_data(job_id, job_data, num_components=40)

    # 2. 使用不同策略创建扩展模式
    extended_patterns = {}

    # 线性缩放策略 (默认)
    extended_scaled = CommunicationPattern.extend_periods(
        original_pattern,
        num_periods=3,
        components_strategy='scaled'  # 分量数是原始数的3倍
    )

    # 次线性缩放策略
    extended_sublinear = CommunicationPattern.extend_periods(
        original_pattern,
        num_periods=3,
        components_strategy='sublinear'  # 分量数是原始数的(1+log(3))倍
    )

    # 保持原始分量数
    extended_original = CommunicationPattern.extend_periods(
        original_pattern,
        num_periods=3,
        components_strategy='original'  # 保持与原始模式相同的分量数
    )

    # 使用自定义分量数
    extended_custom = CommunicationPattern.extend_periods(
        original_pattern,
        num_periods=3,
        components_strategy='custom',
        custom_components=100  # 精确指定分量数为100
    )

    # 3. 生成可视化
    output_dir = "fourier_analysis_results"
    os.makedirs(output_dir, exist_ok=True)

    # 原始模式的频谱分析
    original_pattern.visualize_spectrum(output_dir)

    # 扩展模式的频谱分析与原始模式比较
    for name, pattern in [
        ("scaled", extended_scaled),
        ("sublinear", extended_sublinear),
        ("original", extended_original),
        ("custom", extended_custom)
    ]:
        pattern.visualize_spectrum(output_dir)
        pattern.visualize_original_vs_extended(original_pattern, output_dir)

    print("Analysis complete!")