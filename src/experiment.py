import json
import os
from datetime import datetime

import numpy as np
import torch

from src.contention_metrics import ContentionMetrics
from src.objective_function import ObjectiveFunction

from src.communication import CommunicationPattern
from src.data_generator import DataGenerator
from src.schedule_optimizer import ScheduleOptimizer
from src.visualizer import EnhancedScheduleVisualizer
from src.window_scheduler import WindowScheduler


def run_contention_optimization_experiment(
        # 数据生成参数
        num_jobs: int = 4, # 确定争用的任务数量
        seed: int = 42,
        period_length: float = 100.0,

        # 调度器参数
        bandwidth_capacity: float = 1.0,
        tau: float = 0.5,
        num_windows: int = 20,

        # 度量函数参数
        alpha: float = 1,
        beta: float = 1,
        gamma: float = 2,
        mu: float = 0.1,

        # 目标函数参数
        lambda1: float = 0.01,
        lambda2: float = 0.01,
        epsilon: float = 1e-3,
        num_time_points: int = 1000,

        # 优化器参数
        learning_rate: float = 0.01,
        max_iterations: int = 3000,
        convergence_threshold: float = 1e-8,

        # 控制参数
        device: str = "cuda" if torch.cuda.is_available() else "cpu",
        save_results: bool = True,
        results_dir: str = "../results",
        experiment_name: str = None,
        visualize: bool = True
) -> dict:
    """
    执行通信争用优化实验的完整流程。

    Args:
        num_jobs: 作业数量，指定要在实验中生成和调度的作业总数
        seed: 随机种子，用于确保实验的可重复性
        period_length: 通信模式周期长度，定义单个通信周期的时间跨度

        bandwidth_capacity: 带宽容量上限，系统可用的最大带宽资源
        tau: 平滑参数，用于窗口函数的边缘平滑处理，控制窗口边界平滑程度
        num_windows: 每个作业的窗口数量，每个作业分配的时间窗口数

        alpha: 争用惩罚系数，控制对带宽超额使用的惩罚力度
        beta: 缓冲系数，用于调整需求计算中的安全边界
        gamma: 需求平滑因子，控制需求计算中的平滑程度
        mu: 争用评估权重，在争用计算中平衡不同指标的重要性

        lambda1: 窗口宽度正则化强度，控制对窗口宽度的约束力度
        lambda2: 窗口间隔正则化强度，控制对窗口间隔的约束力度
        epsilon: 数值积分精度参数，控制积分计算的精度
        num_time_points: 积分评估点数量，用于离散化积分计算的点数

        learning_rate: 优化器学习率，控制参数更新步长
        max_iterations: 最大迭代次数，优化过程的最大迭代上限
        convergence_threshold: 收敛阈值，判断优化过程是否收敛的标准

        device: 计算设备，指定使用CPU或GPU进行计算
        save_results: 是否保存结果，控制是否将实验结果写入文件
        results_dir: 结果保存目录，指定保存实验结果的文件夹路径
        experiment_name: 实验名称，用于结果文件命名，如果为None则自动生成
        visualize: 是否生成可视化图表，控制是否创建结果可视化

    Returns:
        dict: 包含实验结果和性能指标的字典
    """
    # 实验准备阶段
    ## 设置随机种子和计算设备
    torch.manual_seed(seed)
    np.random.seed(seed)
    device = torch.device(device)

    ## 创建结果目录
    if save_results and not os.path.exists(results_dir):
        os.makedirs(results_dir)

    ## 生成实验唯一标识
    if experiment_name is None:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        experiment_name = f"experiment_{num_jobs}jobs_{num_windows}windows_{timestamp}"








    # 正式实验阶段
    ## 1. 数据生成
    data_generator = DataGenerator(random_seed=seed)
    dataset = data_generator.generate_test_dataset(num_jobs=num_jobs)
    print(f"已生成{len(dataset)}个作业的测试数据集")

    ## 2. 通信模式建模
    communication_patterns = {}
    for job_id, raw_data in dataset.items():
        pattern = CommunicationPattern.from_raw_data(
            job_id=job_id,
            raw_data=raw_data,
        )
        pattern = CommunicationPattern.extend_periods(pattern, num_periods=3,components_strategy='scaled')
        pattern.total_period_length = pattern.end_time - pattern.start_time
        communication_patterns[job_id] = pattern
    print(f"已完成{len(communication_patterns)}个作业的通信模式建模")

    ## 3. 调度器设置与窗口初始化
    # 创建调度器实例
    scheduler = WindowScheduler(bandwidth_capacity=bandwidth_capacity, tau=tau)

    # 添加所有作业到调度器
    for job_id, pattern in communication_patterns.items():
        scheduler.add_job(job_id=job_id, pattern=pattern)
    print(f"已将{len(scheduler.jobs)}个作业添加到调度器")

    # 初始化窗口参数
    window_params = scheduler.initialize_windows(
        num_windows=num_windows,
        random_init=True  # 可选参数，控制是否使用随机初始化
    )
    print(f"已为每个作业初始化{num_windows}个时隙窗口")

    ## 4. 度量函数和目标函数设置
    contention_metrics = ContentionMetrics(
        alpha=alpha,
        beta=beta,
        gamma=gamma,
        mu=mu
    )
    print(f"已创建争用度量函数，参数: alpha={alpha}, beta={beta}, gamma={gamma}, mu={mu}")

    objective_function = ObjectiveFunction(
        scheduler=scheduler,
        metrics=contention_metrics,
        lambda1=lambda1,
        lambda2=lambda2,
        epsilon=epsilon,
        num_time_points=num_time_points
    )
    print(f"已创建目标函数，参数: lambda1={lambda1}, lambda2={lambda2}, epsilon={epsilon}")

    ## 5. 优化器配置和执行
    # 创建优化器实例
    optimizer = ScheduleOptimizer(
        objective_function=objective_function,
        learning_rate=learning_rate,
        max_iterations=max_iterations,
        convergence_threshold=convergence_threshold
    )

    # 直接初始化优化器，使用scheduler中已有的参数
    optimizer.initialize_optimizer()
    print(f"已配置优化器，学习率: {learning_rate}, 最大迭代次数: {max_iterations}")

    # 可选: 如果需要监视或修改初始参数
    initial_objective = objective_function(window_params)
    print(f"初始目标函数值: {initial_objective:.6f}")

    ## 执行优化
    print(f"开始优化过程...")
    best_params, best_objective, history = optimizer.optimize()
    print(f"优化完成，迭代次数: {len(history) - 1}, 最终目标函数值: {best_objective:.6f}")

    # 设置最优参数到调度器
    scheduler.set_window_params(best_params)
    print(f"已将最优参数应用到调度器")

    # 创建增强型可视化器
    visualizer = EnhancedScheduleVisualizer(scheduler)

    # 1. 按作业分开可视化
    visualizer.visualize_jobs(output_path="../history_experiment_results/job_comparison.png")

    # 2. 所有作业组合可视化
    visualizer.visualize_combined(output_path="../history_experiment_results/combined_visualization.png")
    
    # 3. 调度后需求可视化（无窗口标注）
    visualizer.visualize_scheduled_demands_only(output_path="../history_experiment_results/scheduled_demands_only.png")
    # 4. DFT需求可视化（调度前）
    visualizer.visualize_dft_demands_only(output_path="../history_experiment_results/dft_demands_only.png")


    # ## 6. 结果分析
    # scheduler.set_window_params(best_params)
    # performance = evaluate_bandwidth_usage(scheduler)

    ## 7. 可视化（如果需要）
    # if visualize:
    #     fig_path = os.path.join(results_dir, f"{experiment_name}_optimization.png")
    #     visualize_optimization_history(history, save_path=fig_path)
    #
    #     fig_path = os.path.join(results_dir, f"{experiment_name}_demand.png")
    #     visualize_final_demand(scheduler, save_path=fig_path)






    ## 8. 保存结果
    result_data = {
        "parameters": {
            "num_jobs": num_jobs,
            "num_windows": num_windows,
            "bandwidth_capacity": bandwidth_capacity,
            # 其他参数...
        },
        # "performance": performance,
        "optimization": {
            "initial_objective": history[0],
            "final_objective": best_objective,
            "iterations": len(history),
            "history": history
        },
        "best_params": best_params.get_params_tensor().detach().cpu().numpy().tolist()
    }

    if save_results:
        result_path = os.path.join(results_dir, f"{experiment_name}_results.json")
        with open(result_path, "w") as f:
            json.dump(result_data, f, indent=2)

    return result_data

if __name__ == '__main__':
    run_contention_optimization_experiment()