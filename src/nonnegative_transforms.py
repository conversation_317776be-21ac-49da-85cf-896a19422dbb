# nonnegative_transforms.py
import torch
import torch.nn.functional as F


def softplus_forward(x: torch.Tensor, beta: float = 1.0) -> torch.Tensor:
    """
    Softplus变换：将任意实数映射到正实数
    f(x) = (1/beta) * ln(1 + exp(beta * x))

    Args:
        x: 输入张量
        beta: 控制平滑程度的参数

    Returns:
        torch.Tensor: 变换后的非负张量
    """
    return F.softplus(x, beta=beta)


def softplus_inverse(y: torch.Tensor, beta: float = 1.0, eps: float = 1e-6) -> torch.Tensor:
    """
    Softplus逆变换：将正实数映射回原参数空间
    f^(-1)(y) = (1/beta) * ln(exp(beta * y) - 1)

    Args:
        y: 非负输入张量
        beta: 控制平滑程度的参数
        eps: 数值稳定性修正值

    Returns:
        torch.Tensor: 逆变换后的张量
    """
    return torch.log(torch.exp(beta * y) - 1.0 + eps) / beta


def modified_softplus_forward(x, zero_threshold=1e-6):
    """
    修改版softplus前向函数，特殊处理0值

    参数:
        x: 输入张量
        zero_threshold: 小于此阈值的输入被视为0处理

    返回:
        应用修改版softplus后的结果
    """
    # 创建零值掩码
    zero_mask = torch.abs(x) < zero_threshold

    # 对非零值应用标准softplus
    standard_result = torch.log(1 + torch.exp(x))

    # 将结果中的零值部分置为精确的0
    return torch.where(zero_mask, torch.zeros_like(x), standard_result)


def modified_softplus_inverse(y, zero_threshold=1e-6):
    """
    修改版softplus反向函数，特殊处理0值

    参数:
        y: 输入张量
        zero_threshold: 小于此阈值的输入被视为0处理

    返回:
        应用修改版softplus反函数后的结果
    """
    # 创建零值掩码
    zero_mask = torch.abs(y) < zero_threshold

    # 对非零值应用标准softplus反函数
    standard_result = torch.log(torch.exp(y) - 1)

    # 将结果中的零值部分置为精确的0
    return torch.where(zero_mask, torch.zeros_like(y), standard_result)
def log1p_forward(x: torch.Tensor) -> torch.Tensor:
    """
    Log1p变换：将任意实数映射到非负实数
    f(x) = ln(1 + exp(x))

    Args:
        x: 输入张量

    Returns:
        torch.Tensor: 变换后的非负张量
    """
    return torch.log1p(torch.exp(x))


def log1p_inverse(y: torch.Tensor, eps: float = 1e-6) -> torch.Tensor:
    """
    Log1p逆变换：将非负实数映射回原参数空间
    f^(-1)(y) = ln(exp(y) - 1)

    Args:
        y: 非负输入张量
        eps: 数值稳定性修正值

    Returns:
        torch.Tensor: 逆变换后的张量
    """
    return torch.log(torch.exp(y) - 1.0 + eps)